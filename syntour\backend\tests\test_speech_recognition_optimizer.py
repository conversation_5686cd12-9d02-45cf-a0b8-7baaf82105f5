# tests/test_speech_recognition_optimizer.py
"""
语音识别优化器测试
Speech Recognition Optimizer Tests

测试语音识别优化器的各项功能：
- 音频缓冲区管理
- 会话管理
- 音频质量检测
- 性能统计
"""

import pytest
import asyncio
import numpy as np
import time
from unittest.mock import Mock, patch

from app.core.speech_recognition_optimizer import (
    SpeechRecognitionOptimizer,
    AdvancedAudioBuffer,
    AudioMetrics,
    AudioQuality,
    RecognitionMode,
    LanguageDetector
)

class TestAdvancedAudioBuffer:
    """测试高级音频缓冲区"""
    
    def test_buffer_initialization(self):
        """测试缓冲区初始化"""
        buffer = AdvancedAudioBuffer(max_size=1024, sample_rate=16000)
        
        assert buffer.max_size == 1024
        assert buffer.sample_rate == 16000
        assert buffer.channels == 1
        assert len(buffer.buffer) == 0
        assert len(buffer.chunks) == 0
    
    def test_add_chunk(self):
        """测试添加音频块"""
        buffer = AdvancedAudioBuffer(max_size=1024)
        
        # 添加音频数据
        test_data = b'\x00\x01' * 100  # 200 bytes
        result = buffer.add_chunk(test_data)
        
        assert result is True
        assert len(buffer.buffer) == 200
        assert len(buffer.chunks) == 1
        assert buffer.total_received == 200
    
    def test_buffer_trimming(self):
        """测试缓冲区修剪"""
        buffer = AdvancedAudioBuffer(max_size=100)
        
        # 添加超过最大大小的数据
        for i in range(5):
            test_data = b'\x00\x01' * 30  # 60 bytes each
            buffer.add_chunk(test_data)
        
        # 缓冲区应该被修剪
        assert len(buffer.buffer) <= 100
        assert buffer.total_received == 300  # 总接收量不变
    
    def test_get_audio_data(self):
        """测试获取音频数据"""
        buffer = AdvancedAudioBuffer()
        
        test_data = b'\x00\x01' * 100
        buffer.add_chunk(test_data)
        
        # 获取所有数据
        audio_data = buffer.get_audio_data()
        assert audio_data == test_data
        
        # 获取限制时长的数据
        limited_data = buffer.get_audio_data(max_duration=0.5)  # 0.5秒
        assert len(limited_data) <= len(test_data)
    
    def test_audio_metrics(self):
        """测试音频指标计算"""
        buffer = AdvancedAudioBuffer(sample_rate=16000)
        
        # 创建模拟音频数据（16-bit PCM）
        duration = 1.0  # 1秒
        samples = int(16000 * duration)
        audio_array = np.random.randint(-32768, 32767, samples, dtype=np.int16)
        audio_data = audio_array.tobytes()
        
        buffer.add_chunk(audio_data)
        metrics = buffer.get_metrics()
        
        assert isinstance(metrics, AudioMetrics)
        assert metrics.sample_rate == 16000
        assert abs(metrics.duration_seconds - 1.0) < 0.1  # 允许小误差
        assert metrics.volume_level >= 0
        assert metrics.noise_level >= 0
        assert isinstance(metrics.quality, AudioQuality)
    
    def test_silence_detection(self):
        """测试静音检测"""
        buffer = AdvancedAudioBuffer()
        
        # 新缓冲区不应该被认为是静音的
        assert not buffer.is_silent(threshold_seconds=1.0)
        
        # 等待一段时间后应该被认为是静音的
        time.sleep(0.1)
        assert buffer.is_silent(threshold_seconds=0.05)

class TestSpeechRecognitionOptimizer:
    """测试语音识别优化器"""
    
    @pytest.fixture
    def optimizer(self):
        """创建优化器实例"""
        return SpeechRecognitionOptimizer()
    
    def test_create_session(self, optimizer):
        """测试创建会话"""
        client_id = "test_client_001"
        session = optimizer.create_session(client_id, RecognitionMode.REAL_TIME)
        
        assert client_id in optimizer.active_sessions
        assert session['client_id'] == client_id
        assert session['mode'] == RecognitionMode.REAL_TIME
        assert 'buffer' in session
        assert 'created_at' in session
    
    def test_add_audio_data(self, optimizer):
        """测试添加音频数据"""
        client_id = "test_client_002"
        optimizer.create_session(client_id)
        
        test_data = b'\x00\x01' * 100
        result = optimizer.add_audio_data(client_id, test_data)
        
        assert result is True
        
        # 测试不存在的客户端
        result = optimizer.add_audio_data("nonexistent", test_data)
        assert result is False
    
    def test_should_process_recognition(self, optimizer):
        """测试识别处理判断"""
        client_id = "test_client_003"
        optimizer.create_session(client_id)
        
        # 没有足够数据时不应该处理
        should_process, reason = optimizer.should_process_recognition(client_id)
        assert should_process is False
        assert "too short" in reason.lower() or "no speech" in reason.lower()
        
        # 添加足够的高质量音频数据
        # 创建1秒的16kHz音频数据
        samples = 16000
        audio_array = np.random.randint(-16384, 16383, samples, dtype=np.int16)
        audio_data = audio_array.tobytes()
        
        optimizer.add_audio_data(client_id, audio_data)
        
        should_process, reason = optimizer.should_process_recognition(client_id, min_duration=0.5)
        # 结果取决于音频质量检测
        assert isinstance(should_process, bool)
        assert isinstance(reason, str)
    
    def test_get_optimized_audio(self, optimizer):
        """测试获取优化音频"""
        client_id = "test_client_004"
        optimizer.create_session(client_id)
        
        # 没有数据时应该返回None
        audio = optimizer.get_optimized_audio(client_id)
        assert audio is None
        
        # 添加数据后应该返回优化的音频
        test_data = b'\x00\x01' * 1000
        optimizer.add_audio_data(client_id, test_data)
        
        audio = optimizer.get_optimized_audio(client_id)
        assert audio is not None
        assert isinstance(audio, bytes)
    
    def test_update_recognition_stats(self, optimizer):
        """测试更新识别统计"""
        client_id = "test_client_005"
        optimizer.create_session(client_id)
        
        initial_stats = optimizer.get_global_stats()
        initial_total = initial_stats['total_requests']
        
        # 更新成功的识别
        optimizer.update_recognition_stats(
            client_id, success=True, processing_time=1.5, 
            confidence=0.95, detected_language="en-US"
        )
        
        updated_stats = optimizer.get_global_stats()
        assert updated_stats['total_requests'] == initial_total + 1
        assert updated_stats['successful_requests'] == initial_stats['successful_requests'] + 1
        assert "en-US" in updated_stats['languages_detected']
    
    def test_session_cleanup(self, optimizer):
        """测试会话清理"""
        client_id = "test_client_006"
        optimizer.create_session(client_id)
        
        assert client_id in optimizer.active_sessions
        
        optimizer.cleanup_session(client_id)
        assert client_id not in optimizer.active_sessions
    
    def test_inactive_session_cleanup(self, optimizer):
        """测试不活跃会话清理"""
        client_id = "test_client_007"
        session = optimizer.create_session(client_id)
        
        # 模拟旧的活动时间
        session['last_activity'] = time.time() - 400  # 400秒前
        
        optimizer.cleanup_inactive_sessions(timeout_seconds=300)  # 5分钟超时
        
        assert client_id not in optimizer.active_sessions
    
    def test_get_session_metrics(self, optimizer):
        """测试获取会话指标"""
        client_id = "test_client_008"
        optimizer.create_session(client_id)
        
        metrics = optimizer.get_session_metrics(client_id)
        
        assert metrics is not None
        assert metrics['client_id'] == client_id
        assert 'audio_metrics' in metrics
        assert 'buffer_size' in metrics
        assert 'total_received' in metrics
        
        # 测试不存在的会话
        metrics = optimizer.get_session_metrics("nonexistent")
        assert metrics is None

class TestLanguageDetector:
    """测试语言检测器"""
    
    @pytest.fixture
    def detector(self):
        """创建语言检测器实例"""
        return LanguageDetector()
    
    def test_english_detection(self, detector):
        """测试英语检测"""
        english_text = "The quick brown fox jumps over the lazy dog"
        detected = detector.detect_language(english_text)
        assert detected == "en-US"
    
    def test_chinese_detection(self, detector):
        """测试中文检测"""
        chinese_text = "我是一个中国人，我爱我的祖国"
        detected = detector.detect_language(chinese_text)
        assert detected == "zh-CN"
    
    def test_spanish_detection(self, detector):
        """测试西班牙语检测"""
        spanish_text = "El gato está en la mesa"
        detected = detector.detect_language(spanish_text)
        assert detected == "es-ES"
    
    def test_empty_text(self, detector):
        """测试空文本"""
        detected = detector.detect_language("")
        assert detected == "en-US"  # 默认语言
    
    def test_mixed_text(self, detector):
        """测试混合语言文本"""
        mixed_text = "Hello 你好 world"
        detected = detector.detect_language(mixed_text)
        # 应该检测到其中一种语言
        assert detected in ["en-US", "zh-CN"]

@pytest.mark.asyncio
class TestIntegration:
    """集成测试"""
    
    async def test_full_workflow(self):
        """测试完整的工作流程"""
        optimizer = SpeechRecognitionOptimizer()
        client_id = "integration_test_client"
        
        # 1. 创建会话
        session = optimizer.create_session(client_id, RecognitionMode.REAL_TIME)
        assert client_id in optimizer.active_sessions
        
        # 2. 模拟添加音频数据
        for i in range(5):
            # 创建200ms的音频数据
            samples = int(16000 * 0.2)  # 200ms at 16kHz
            audio_array = np.random.randint(-16384, 16383, samples, dtype=np.int16)
            audio_data = audio_array.tobytes()
            
            result = optimizer.add_audio_data(client_id, audio_data)
            assert result is True
            
            await asyncio.sleep(0.1)  # 模拟实时数据流
        
        # 3. 检查是否可以处理
        should_process, reason = optimizer.should_process_recognition(
            client_id, min_duration=0.5
        )
        
        # 4. 获取优化的音频
        if should_process:
            audio = optimizer.get_optimized_audio(client_id)
            assert audio is not None
        
        # 5. 更新统计信息
        optimizer.update_recognition_stats(
            client_id, success=True, processing_time=2.0, 
            confidence=0.85, detected_language="en-US"
        )
        
        # 6. 检查统计信息
        stats = optimizer.get_global_stats()
        assert stats['total_requests'] >= 1
        assert stats['successful_requests'] >= 1
        
        # 7. 获取会话指标
        metrics = optimizer.get_session_metrics(client_id)
        assert metrics is not None
        assert metrics['recognition_count'] == 1
        
        # 8. 清理会话
        optimizer.cleanup_session(client_id)
        assert client_id not in optimizer.active_sessions

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
