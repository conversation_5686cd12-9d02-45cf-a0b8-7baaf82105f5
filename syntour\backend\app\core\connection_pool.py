# app/core/connection_pool.py
import asyncio
import aiohttp
import asyncpg
import redis.asyncio as redis
import logging
import time
from typing import Dict, Optional, Any, List, Callable
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from urllib.parse import urlparse
import os

logger = logging.getLogger(__name__)

@dataclass
class PoolConfig:
    """Configuration for connection pools"""
    min_size: int = 5
    max_size: int = 20
    max_queries: int = 50000
    max_inactive_connection_lifetime: float = 300.0
    timeout: float = 60.0
    command_timeout: float = 60.0
    server_settings: Dict[str, str] = field(default_factory=dict)
    health_check_interval: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0

@dataclass
class PoolStats:
    """Statistics for connection pools"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    total_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_health_check: Optional[float] = None
    health_status: str = "unknown"

class DatabaseConnectionPool:
    """Advanced database connection pool with health monitoring"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.pool: Optional[asyncpg.Pool] = None
        self.stats = PoolStats()
        self._health_check_task: Optional[asyncio.Task] = None
        self._lock = asyncio.Lock()
        
    async def initialize(self, database_url: str):
        """Initialize the database connection pool"""
        try:
            # Parse database URL for logging (without credentials)
            parsed = urlparse(database_url)
            safe_url = f"{parsed.scheme}://{parsed.hostname}:{parsed.port}/{parsed.path.lstrip('/')}"
            logger.info(f"Initializing database pool for {safe_url}")
            
            self.pool = await asyncpg.create_pool(
                database_url,
                min_size=self.config.min_size,
                max_size=self.config.max_size,
                max_queries=self.config.max_queries,
                max_inactive_connection_lifetime=self.config.max_inactive_connection_lifetime,
                timeout=self.config.timeout,
                command_timeout=self.config.command_timeout,
                server_settings=self.config.server_settings or {
                    'jit': 'off',
                    'application_name': 'syntour_backend'
                }
            )
            
            # Start health check task
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            # Update stats
            self.stats.total_connections = self.config.max_size
            self.stats.health_status = "healthy"
            
            logger.info(f"Database pool initialized successfully with {self.config.min_size}-{self.config.max_size} connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            self.stats.health_status = "unhealthy"
            raise
    
    async def _health_check_loop(self):
        """Periodic health check for the connection pool"""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check error: {e}")
                self.stats.health_status = "unhealthy"
    
    async def _perform_health_check(self):
        """Perform health check on the connection pool"""
        if not self.pool:
            self.stats.health_status = "unhealthy"
            return
            
        try:
            async with self.pool.acquire() as connection:
                await connection.execute("SELECT 1")
                
            # Update stats
            self.stats.last_health_check = time.time()
            self.stats.health_status = "healthy"
            self.stats.active_connections = len(self.pool._holders) if hasattr(self.pool, '_holders') else 0
            self.stats.idle_connections = self.stats.total_connections - self.stats.active_connections
            
            logger.debug(f"Database health check passed. Active: {self.stats.active_connections}, Idle: {self.stats.idle_connections}")
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            self.stats.health_status = "unhealthy"
    
    @asynccontextmanager
    async def acquire(self):
        """Acquire a database connection with automatic retry"""
        if not self.pool:
            raise RuntimeError("Database pool not initialized")
        
        start_time = time.time()
        retries = 0
        
        while retries <= self.config.max_retries:
            try:
                async with self.pool.acquire() as connection:
                    # Update stats
                    self.stats.total_requests += 1
                    response_time = time.time() - start_time
                    self.stats.avg_response_time = (
                        (self.stats.avg_response_time * (self.stats.total_requests - 1) + response_time) 
                        / self.stats.total_requests
                    )
                    
                    yield connection
                    return
                    
            except Exception as e:
                retries += 1
                self.stats.failed_requests += 1
                
                if retries > self.config.max_retries:
                    logger.error(f"Failed to acquire database connection after {retries} retries: {e}")
                    raise
                
                logger.warning(f"Database connection attempt {retries} failed: {e}, retrying...")
                await asyncio.sleep(self.config.retry_delay * retries)
    
    async def execute_query(self, query: str, *args, **kwargs):
        """Execute a query with connection management"""
        async with self.acquire() as connection:
            return await connection.fetch(query, *args, **kwargs)
    
    async def execute_command(self, command: str, *args, **kwargs):
        """Execute a command with connection management"""
        async with self.acquire() as connection:
            return await connection.execute(command, *args, **kwargs)
    
    def get_stats(self) -> PoolStats:
        """Get current pool statistics"""
        return self.stats
    
    async def close(self):
        """Close the connection pool"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        if self.pool:
            await self.pool.close()
            logger.info("Database connection pool closed")

class HTTPConnectionPool:
    """Advanced HTTP connection pool with health monitoring"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.stats = PoolStats()
        self._health_check_task: Optional[asyncio.Task] = None
        
    async def initialize(self):
        """Initialize the HTTP connection pool"""
        try:
            connector = aiohttp.TCPConnector(
                limit=self.config.max_size,
                limit_per_host=self.config.max_size // 4,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            timeout = aiohttp.ClientTimeout(
                total=self.config.timeout,
                connect=self.config.timeout / 2
            )
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={'User-Agent': 'SynTour/2.0 (Enhanced Connection Pool)'}
            )
            
            # Start health check task
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.stats.total_connections = self.config.max_size
            self.stats.health_status = "healthy"
            
            logger.info(f"HTTP connection pool initialized with {self.config.max_size} max connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize HTTP pool: {e}")
            self.stats.health_status = "unhealthy"
            raise
    
    async def _health_check_loop(self):
        """Periodic health check for the HTTP pool"""
        while True:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"HTTP health check error: {e}")
                self.stats.health_status = "unhealthy"
    
    async def _perform_health_check(self):
        """Perform health check on the HTTP pool"""
        if not self.session:
            self.stats.health_status = "unhealthy"
            return
            
        try:
            # Simple connectivity test
            connector = self.session.connector
            if hasattr(connector, '_conns'):
                self.stats.active_connections = sum(len(conns) for conns in connector._conns.values())
            
            self.stats.last_health_check = time.time()
            self.stats.health_status = "healthy"
            
            logger.debug(f"HTTP health check passed. Active connections: {self.stats.active_connections}")
            
        except Exception as e:
            logger.error(f"HTTP health check failed: {e}")
            self.stats.health_status = "unhealthy"
    
    async def request(self, method: str, url: str, **kwargs):
        """Make HTTP request with connection management"""
        if not self.session:
            raise RuntimeError("HTTP pool not initialized")
        
        start_time = time.time()
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                # Update stats
                self.stats.total_requests += 1
                response_time = time.time() - start_time
                self.stats.avg_response_time = (
                    (self.stats.avg_response_time * (self.stats.total_requests - 1) + response_time) 
                    / self.stats.total_requests
                )
                
                return response
                
        except Exception as e:
            self.stats.failed_requests += 1
            logger.error(f"HTTP request failed: {e}")
            raise
    
    def get_stats(self) -> PoolStats:
        """Get current pool statistics"""
        return self.stats
    
    async def close(self):
        """Close the HTTP connection pool"""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
            logger.info("HTTP connection pool closed")

class RedisConnectionPool:
    """Redis connection pool for caching"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.pool: Optional[redis.ConnectionPool] = None
        self.client: Optional[redis.Redis] = None
        self.stats = PoolStats()
        
    async def initialize(self, redis_url: str = None):
        """Initialize Redis connection pool"""
        try:
            redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379')
            
            self.pool = redis.ConnectionPool.from_url(
                redis_url,
                max_connections=self.config.max_size,
                retry_on_timeout=True,
                socket_timeout=self.config.timeout,
                socket_connect_timeout=self.config.timeout / 2
            )
            
            self.client = redis.Redis(connection_pool=self.pool)
            
            # Test connection
            await self.client.ping()
            
            self.stats.total_connections = self.config.max_size
            self.stats.health_status = "healthy"
            
            logger.info(f"Redis connection pool initialized with {self.config.max_size} max connections")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis pool: {e}")
            self.stats.health_status = "unhealthy"
            # Don't raise - Redis is optional for caching
    
    async def get(self, key: str):
        """Get value from Redis"""
        if not self.client:
            return None
        
        try:
            return await self.client.get(key)
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    async def set(self, key: str, value: str, ex: int = None):
        """Set value in Redis"""
        if not self.client:
            return False
        
        try:
            return await self.client.set(key, value, ex=ex)
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    def get_stats(self) -> PoolStats:
        """Get current pool statistics"""
        return self.stats
    
    async def close(self):
        """Close Redis connection pool"""
        if self.client:
            await self.client.close()
            logger.info("Redis connection pool closed")

class ConnectionPoolManager:
    """Centralized manager for all connection pools"""

    def __init__(self):
        self.database_pool: Optional[DatabaseConnectionPool] = None
        self.http_pool: Optional[HTTPConnectionPool] = None
        self.redis_pool: Optional[RedisConnectionPool] = None
        self._initialized = False

    async def initialize(self,
                        database_url: Optional[str] = None,
                        redis_url: Optional[str] = None,
                        pool_config: Optional[PoolConfig] = None):
        """Initialize all connection pools"""
        if self._initialized:
            logger.warning("Connection pools already initialized")
            return

        config = pool_config or PoolConfig()

        try:
            # Initialize HTTP pool (always needed)
            self.http_pool = HTTPConnectionPool(config)
            await self.http_pool.initialize()

            # Initialize database pool if URL provided
            if database_url:
                self.database_pool = DatabaseConnectionPool(config)
                await self.database_pool.initialize(database_url)

            # Initialize Redis pool if URL provided
            if redis_url:
                self.redis_pool = RedisConnectionPool(config)
                await self.redis_pool.initialize(redis_url)

            self._initialized = True
            logger.info("All connection pools initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize connection pools: {e}")
            await self.close()
            raise

    def get_database_pool(self) -> Optional[DatabaseConnectionPool]:
        """Get database connection pool"""
        return self.database_pool

    def get_http_pool(self) -> Optional[HTTPConnectionPool]:
        """Get HTTP connection pool"""
        return self.http_pool

    def get_redis_pool(self) -> Optional[RedisConnectionPool]:
        """Get Redis connection pool"""
        return self.redis_pool

    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all pools"""
        status = {
            "initialized": self._initialized,
            "pools": {}
        }

        if self.http_pool:
            status["pools"]["http"] = {
                "status": self.http_pool.get_stats().health_status,
                "stats": self.http_pool.get_stats().__dict__
            }

        if self.database_pool:
            status["pools"]["database"] = {
                "status": self.database_pool.get_stats().health_status,
                "stats": self.database_pool.get_stats().__dict__
            }

        if self.redis_pool:
            status["pools"]["redis"] = {
                "status": self.redis_pool.get_stats().health_status,
                "stats": self.redis_pool.get_stats().__dict__
            }

        return status

    async def close(self):
        """Close all connection pools"""
        if self.database_pool:
            await self.database_pool.close()

        if self.http_pool:
            await self.http_pool.close()

        if self.redis_pool:
            await self.redis_pool.close()

        self._initialized = False
        logger.info("All connection pools closed")

# Global connection pool manager instance
connection_pool_manager = ConnectionPoolManager()
