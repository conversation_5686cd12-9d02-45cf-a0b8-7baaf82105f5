# app/core/enhanced_backend_manager.py
import asyncio
import logging
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
import os

# Import all the enhanced systems
from .connection_pool import connection_pool_manager, PoolConfig
from .circuit_breaker import circuit_breaker_manager, CircuitBreakerConfig
from .async_task_manager import task_manager, TaskConfig, TaskPriority
from .error_recovery import error_recovery_system
from .health_monitor import health_monitor, HealthCheck
from .response_formatter import response_formatter, ResponseFormatter
from .i18n import i18n_manager
from .content_length_controller import content_length_controller, responsive_formatter
from .enhanced_api_client import EnhancedAPIClient

logger = logging.getLogger(__name__)

class EnhancedBackendManager:
    """Centralized manager for all enhanced backend systems"""
    
    def __init__(self):
        self.initialized = False
        self.systems_status: Dict[str, str] = {}
        
        # System components
        self.connection_manager = connection_pool_manager
        self.circuit_manager = circuit_breaker_manager
        self.task_manager = task_manager
        self.error_recovery = error_recovery_system
        self.health_monitor = health_monitor
        self.response_formatter = response_formatter
        self.i18n_manager = i18n_manager
        self.content_controller = content_length_controller
        self.responsive_formatter = responsive_formatter
        
        # Enhanced API clients
        self.api_clients: Dict[str, EnhancedAPIClient] = {}
        
        # Configuration
        self.config = {
            "database_url": os.getenv("DATABASE_URL"),
            "redis_url": os.getenv("REDIS_URL"),
            "default_language": os.getenv("DEFAULT_LANGUAGE", "en"),
            "max_concurrent_tasks": int(os.getenv("MAX_CONCURRENT_TASKS", "10")),
            "health_check_interval": float(os.getenv("HEALTH_CHECK_INTERVAL", "30.0"))
        }
    
    async def initialize(self):
        """Initialize all backend systems"""
        if self.initialized:
            logger.warning("Backend manager already initialized")
            return
        
        logger.info("Initializing enhanced backend systems...")
        
        try:
            # Initialize connection pools
            await self._initialize_connection_pools()
            
            # Initialize task manager
            await self._initialize_task_manager()
            
            # Initialize health monitoring
            await self._initialize_health_monitoring()
            
            # Initialize error recovery
            await self._initialize_error_recovery()
            
            # Initialize response formatting
            await self._initialize_response_formatting()
            
            # Initialize API clients
            await self._initialize_api_clients()
            
            self.initialized = True
            logger.info("Enhanced backend systems initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize backend systems: {e}")
            await self.shutdown()
            raise
    
    async def _initialize_connection_pools(self):
        """Initialize connection pools"""
        try:
            pool_config = PoolConfig(
                min_size=5,
                max_size=20,
                timeout=30.0,
                health_check_interval=self.config["health_check_interval"]
            )
            
            await self.connection_manager.initialize(
                database_url=self.config["database_url"],
                redis_url=self.config["redis_url"],
                pool_config=pool_config
            )
            
            self.systems_status["connection_pools"] = "healthy"
            logger.info("Connection pools initialized")
            
        except Exception as e:
            self.systems_status["connection_pools"] = "unhealthy"
            logger.error(f"Failed to initialize connection pools: {e}")
            raise
    
    async def _initialize_task_manager(self):
        """Initialize async task manager"""
        try:
            await self.task_manager.start()
            self.systems_status["task_manager"] = "healthy"
            logger.info("Task manager initialized")
            
        except Exception as e:
            self.systems_status["task_manager"] = "unhealthy"
            logger.error(f"Failed to initialize task manager: {e}")
            raise
    
    async def _initialize_health_monitoring(self):
        """Initialize health monitoring"""
        try:
            # Add custom health checks
            self._add_custom_health_checks()
            
            # Start monitoring
            await self.health_monitor.start_monitoring()
            
            self.systems_status["health_monitor"] = "healthy"
            logger.info("Health monitoring initialized")
            
        except Exception as e:
            self.systems_status["health_monitor"] = "unhealthy"
            logger.error(f"Failed to initialize health monitoring: {e}")
            raise
    
    def _add_custom_health_checks(self):
        """Add custom health checks for the application"""
        
        # Database health check
        if self.config["database_url"]:
            self.health_monitor.add_health_check(HealthCheck(
                name="database",
                check_func=self._check_database_health,
                interval=30.0,
                critical=True
            ))
        
        # Redis health check
        if self.config["redis_url"]:
            self.health_monitor.add_health_check(HealthCheck(
                name="redis",
                check_func=self._check_redis_health,
                interval=30.0,
                critical=False
            ))
        
        # Task manager health check
        self.health_monitor.add_health_check(HealthCheck(
            name="task_manager",
            check_func=self._check_task_manager_health,
            interval=60.0,
            critical=True
        ))
        
        # Circuit breaker health check
        self.health_monitor.add_health_check(HealthCheck(
            name="circuit_breakers",
            check_func=self._check_circuit_breakers_health,
            interval=60.0,
            critical=False
        ))
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database health"""
        try:
            db_pool = self.connection_manager.get_database_pool()
            if not db_pool:
                return {"status": "unhealthy", "message": "Database pool not available"}
            
            async with db_pool.acquire() as connection:
                await connection.execute("SELECT 1")
            
            stats = db_pool.get_stats()
            return {
                "status": "healthy",
                "message": "Database connection successful",
                "details": {
                    "active_connections": stats.active_connections,
                    "total_requests": stats.total_requests,
                    "avg_response_time": stats.avg_response_time
                }
            }
            
        except Exception as e:
            return {"status": "unhealthy", "message": f"Database check failed: {e}"}
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """Check Redis health"""
        try:
            redis_pool = self.connection_manager.get_redis_pool()
            if not redis_pool:
                return {"status": "degraded", "message": "Redis pool not available"}
            
            await redis_pool.set("health_check", "ok", ex=10)
            result = await redis_pool.get("health_check")
            
            if result == b"ok":
                return {"status": "healthy", "message": "Redis connection successful"}
            else:
                return {"status": "degraded", "message": "Redis response unexpected"}
                
        except Exception as e:
            return {"status": "degraded", "message": f"Redis check failed: {e}"}
    
    async def _check_task_manager_health(self) -> Dict[str, Any]:
        """Check task manager health"""
        try:
            stats = self.task_manager.get_stats()
            
            # Check if task manager is responsive
            test_task_id = await self.task_manager.submit_task(
                lambda: "health_check",
                config=TaskConfig(priority=TaskPriority.HIGH)
            )
            
            result = await self.task_manager.wait_for_task(test_task_id, timeout=5.0)
            
            if result.result == "health_check":
                return {
                    "status": "healthy",
                    "message": "Task manager responsive",
                    "details": {
                        "queue_size": stats["queue_size"],
                        "running_tasks": stats["running_tasks"],
                        "completed_tasks": stats["completed_tasks"],
                        "failed_tasks": stats["failed_tasks"]
                    }
                }
            else:
                return {"status": "degraded", "message": "Task manager slow response"}
                
        except Exception as e:
            return {"status": "unhealthy", "message": f"Task manager check failed: {e}"}
    
    async def _check_circuit_breakers_health(self) -> Dict[str, Any]:
        """Check circuit breakers health"""
        try:
            summary = self.circuit_manager.get_health_summary()
            
            if summary["unhealthy"] > 0:
                status = "degraded"
                message = f"{summary['unhealthy']} circuit breakers are open"
            elif summary["degraded"] > 0:
                status = "degraded"
                message = f"{summary['degraded']} circuit breakers are half-open"
            else:
                status = "healthy"
                message = "All circuit breakers are closed"
            
            return {
                "status": status,
                "message": message,
                "details": summary
            }
            
        except Exception as e:
            return {"status": "unknown", "message": f"Circuit breaker check failed: {e}"}
    
    async def _initialize_error_recovery(self):
        """Initialize error recovery system"""
        try:
            # Add alert handlers
            self.error_recovery.add_alert_handler(self._handle_error_alert)
            
            self.systems_status["error_recovery"] = "healthy"
            logger.info("Error recovery system initialized")
            
        except Exception as e:
            self.systems_status["error_recovery"] = "unhealthy"
            logger.error(f"Failed to initialize error recovery: {e}")
            raise
    
    async def _handle_error_alert(self, alert_data: Dict[str, Any]):
        """Handle error alerts"""
        logger.error(f"Error alert: {alert_data}")
        # Here you could integrate with external alerting systems
        # like Slack, email, PagerDuty, etc.
    
    async def _initialize_response_formatting(self):
        """Initialize response formatting and i18n"""
        try:
            # Set default language
            self.i18n_manager.set_language(self.config["default_language"])
            self.response_formatter.set_language(self.config["default_language"])
            
            self.systems_status["response_formatting"] = "healthy"
            logger.info("Response formatting initialized")
            
        except Exception as e:
            self.systems_status["response_formatting"] = "unhealthy"
            logger.error(f"Failed to initialize response formatting: {e}")
            raise
    
    async def _initialize_api_clients(self):
        """Initialize enhanced API clients"""
        try:
            # Initialize API clients for external services
            api_configs = {
                "amadeus": {
                    "base_url": os.getenv("AMADEUS_BASE_URL", "https://test.api.amadeus.com"),
                    "cache_enabled": True,
                    "cache_ttl": 300
                },
                "openweathermap": {
                    "base_url": "https://api.openweathermap.org",
                    "cache_enabled": True,
                    "cache_ttl": 600
                },
                "geoapify": {
                    "base_url": "https://api.geoapify.com",
                    "cache_enabled": True,
                    "cache_ttl": 3600
                }
            }
            
            for api_name, config in api_configs.items():
                client = EnhancedAPIClient(api_name, config["base_url"], config)
                await client.initialize()
                self.api_clients[api_name] = client
            
            self.systems_status["api_clients"] = "healthy"
            logger.info(f"Initialized {len(self.api_clients)} enhanced API clients")
            
        except Exception as e:
            self.systems_status["api_clients"] = "unhealthy"
            logger.error(f"Failed to initialize API clients: {e}")
            raise
    
    def get_api_client(self, api_name: str) -> Optional[EnhancedAPIClient]:
        """Get an enhanced API client"""
        return self.api_clients.get(api_name)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        return {
            "initialized": self.initialized,
            "systems": self.systems_status,
            "health_summary": self.health_monitor.get_overall_health() if self.initialized else None,
            "task_manager_stats": self.task_manager.get_stats() if self.initialized else None,
            "circuit_breaker_summary": self.circuit_manager.get_health_summary() if self.initialized else None,
            "connection_pool_status": self.connection_manager.get_health_status() if self.initialized else None
        }
    
    async def shutdown(self):
        """Shutdown all backend systems"""
        logger.info("Shutting down enhanced backend systems...")
        
        # Stop health monitoring
        if self.health_monitor:
            await self.health_monitor.stop_monitoring()
        
        # Stop task manager
        if self.task_manager:
            await self.task_manager.stop()
        
        # Close API clients
        for client in self.api_clients.values():
            await client.close()
        
        # Close connection pools
        if self.connection_manager:
            await self.connection_manager.close()
        
        self.initialized = False
        logger.info("Enhanced backend systems shutdown complete")

# Global enhanced backend manager instance
enhanced_backend_manager = EnhancedBackendManager()

# Context manager for application lifecycle
@asynccontextmanager
async def enhanced_backend_lifespan():
    """Context manager for enhanced backend lifecycle"""
    try:
        await enhanced_backend_manager.initialize()
        yield enhanced_backend_manager
    finally:
        await enhanced_backend_manager.shutdown()
