# app/core/content_length_controller.py
import re
import logging
import hashlib
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import time

logger = logging.getLogger(__name__)

class DeviceType(str, Enum):
    """Device types for content optimization"""
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"
    SMART_WATCH = "smart_watch"
    SMART_TV = "smart_tv"
    UNKNOWN = "unknown"

class ContentType(str, Enum):
    """Types of content for length control"""
    AI_RESPONSE = "ai_response"
    TRAVEL_PLAN = "travel_plan"
    API_DATA = "api_data"
    ERROR_MESSAGE = "error_message"
    NOTIFICATION = "notification"

class SummaryLevel(str, Enum):
    """Summary detail levels"""
    BRIEF = "brief"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

@dataclass
class LengthLimits:
    """Length limits for different content types and devices"""
    max_characters: int
    max_words: int
    max_sentences: int
    max_paragraphs: int
    truncation_strategy: str = "smart"  # smart, hard, summary

@dataclass
class DeviceProfile:
    """Profile for device-specific content optimization"""
    device_type: DeviceType
    screen_width: Optional[int] = None
    screen_height: Optional[int] = None
    is_touch: bool = True
    supports_scrolling: bool = True
    preferred_font_size: int = 14
    reading_speed_wpm: int = 200  # words per minute
    attention_span_seconds: int = 30

@dataclass
class ContentMetrics:
    """Metrics for content analysis"""
    character_count: int
    word_count: int
    sentence_count: int
    paragraph_count: int
    reading_time_seconds: float
    complexity_score: float
    key_points: List[str] = field(default_factory=list)

@dataclass
class TruncationResult:
    """Result of content truncation"""
    original_content: str
    truncated_content: str
    is_truncated: bool
    truncation_method: str
    continuation_token: Optional[str] = None
    summary: Optional[str] = None
    metrics: Optional[ContentMetrics] = None

class ContentLengthController:
    """Intelligent content length management system"""
    
    def __init__(self):
        # Device-specific length limits
        self.device_limits: Dict[DeviceType, Dict[ContentType, LengthLimits]] = {}
        
        # User preferences storage
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Content cache for continuation
        self.content_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 3600  # 1 hour
        
        # Setup default limits
        self._setup_default_limits()
        
        # Summarization patterns
        self.summary_patterns = {
            "key_points": r"(?:^|\n)[-•*]\s*(.+?)(?=\n|$)",
            "numbered_points": r"(?:^|\n)\d+\.\s*(.+?)(?=\n|$)",
            "headings": r"(?:^|\n)(#{1,6}\s*.+?)(?=\n|$)",
            "important": r"\*\*(.*?)\*\*|__(.*?)__|<strong>(.*?)</strong>",
            "sentences": r"[.!?]+\s+"
        }
    
    def _setup_default_limits(self):
        """Setup default length limits for different devices and content types"""
        
        # Mobile limits
        mobile_limits = {
            ContentType.AI_RESPONSE: LengthLimits(
                max_characters=500,
                max_words=100,
                max_sentences=5,
                max_paragraphs=2,
                truncation_strategy="smart"
            ),
            ContentType.TRAVEL_PLAN: LengthLimits(
                max_characters=800,
                max_words=150,
                max_sentences=8,
                max_paragraphs=3,
                truncation_strategy="summary"
            ),
            ContentType.API_DATA: LengthLimits(
                max_characters=300,
                max_words=60,
                max_sentences=3,
                max_paragraphs=1,
                truncation_strategy="hard"
            ),
            ContentType.ERROR_MESSAGE: LengthLimits(
                max_characters=150,
                max_words=30,
                max_sentences=2,
                max_paragraphs=1,
                truncation_strategy="hard"
            )
        }
        
        # Tablet limits
        tablet_limits = {
            ContentType.AI_RESPONSE: LengthLimits(
                max_characters=1000,
                max_words=200,
                max_sentences=10,
                max_paragraphs=4,
                truncation_strategy="smart"
            ),
            ContentType.TRAVEL_PLAN: LengthLimits(
                max_characters=1500,
                max_words=300,
                max_sentences=15,
                max_paragraphs=5,
                truncation_strategy="summary"
            ),
            ContentType.API_DATA: LengthLimits(
                max_characters=600,
                max_words=120,
                max_sentences=6,
                max_paragraphs=2,
                truncation_strategy="smart"
            ),
            ContentType.ERROR_MESSAGE: LengthLimits(
                max_characters=200,
                max_words=40,
                max_sentences=3,
                max_paragraphs=1,
                truncation_strategy="hard"
            )
        }
        
        # Desktop limits
        desktop_limits = {
            ContentType.AI_RESPONSE: LengthLimits(
                max_characters=2000,
                max_words=400,
                max_sentences=20,
                max_paragraphs=8,
                truncation_strategy="smart"
            ),
            ContentType.TRAVEL_PLAN: LengthLimits(
                max_characters=3000,
                max_words=600,
                max_sentences=30,
                max_paragraphs=10,
                truncation_strategy="summary"
            ),
            ContentType.API_DATA: LengthLimits(
                max_characters=1200,
                max_words=240,
                max_sentences=12,
                max_paragraphs=4,
                truncation_strategy="smart"
            ),
            ContentType.ERROR_MESSAGE: LengthLimits(
                max_characters=300,
                max_words=60,
                max_sentences=4,
                max_paragraphs=2,
                truncation_strategy="hard"
            )
        }
        
        # Smart watch limits
        watch_limits = {
            ContentType.AI_RESPONSE: LengthLimits(
                max_characters=100,
                max_words=20,
                max_sentences=2,
                max_paragraphs=1,
                truncation_strategy="summary"
            ),
            ContentType.NOTIFICATION: LengthLimits(
                max_characters=50,
                max_words=10,
                max_sentences=1,
                max_paragraphs=1,
                truncation_strategy="hard"
            ),
            ContentType.ERROR_MESSAGE: LengthLimits(
                max_characters=80,
                max_words=15,
                max_sentences=1,
                max_paragraphs=1,
                truncation_strategy="hard"
            )
        }
        
        self.device_limits = {
            DeviceType.MOBILE: mobile_limits,
            DeviceType.TABLET: tablet_limits,
            DeviceType.DESKTOP: desktop_limits,
            DeviceType.SMART_WATCH: watch_limits,
            DeviceType.UNKNOWN: mobile_limits  # Default to mobile for unknown devices
        }
    
    def detect_device_type(self, user_agent: str, screen_width: Optional[int] = None) -> DeviceType:
        """Detect device type from user agent and screen width"""
        user_agent_lower = user_agent.lower()
        
        # Smart watch detection
        if any(watch in user_agent_lower for watch in ['watch', 'wearos', 'watchos']):
            return DeviceType.SMART_WATCH
        
        # Smart TV detection
        if any(tv in user_agent_lower for tv in ['tv', 'television', 'smarttv', 'roku', 'chromecast']):
            return DeviceType.SMART_TV
        
        # Mobile detection
        if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone', 'ipod']):
            return DeviceType.MOBILE
        
        # Tablet detection
        if any(tablet in user_agent_lower for tablet in ['tablet', 'ipad']):
            return DeviceType.TABLET
        
        # Screen width based detection
        if screen_width:
            if screen_width < 768:
                return DeviceType.MOBILE
            elif screen_width < 1024:
                return DeviceType.TABLET
            else:
                return DeviceType.DESKTOP
        
        return DeviceType.UNKNOWN
    
    def analyze_content(self, content: str) -> ContentMetrics:
        """Analyze content to extract metrics"""
        # Basic counts
        character_count = len(content)
        words = re.findall(r'\b\w+\b', content)
        word_count = len(words)
        sentences = re.split(r'[.!?]+', content)
        sentence_count = len([s for s in sentences if s.strip()])
        paragraphs = content.split('\n\n')
        paragraph_count = len([p for p in paragraphs if p.strip()])
        
        # Reading time calculation (average 200 WPM)
        reading_time_seconds = (word_count / 200) * 60
        
        # Complexity score (simple heuristic)
        avg_word_length = sum(len(word) for word in words) / max(word_count, 1)
        avg_sentence_length = word_count / max(sentence_count, 1)
        complexity_score = (avg_word_length * 0.3 + avg_sentence_length * 0.7) / 10
        
        # Extract key points
        key_points = self._extract_key_points(content)
        
        return ContentMetrics(
            character_count=character_count,
            word_count=word_count,
            sentence_count=sentence_count,
            paragraph_count=paragraph_count,
            reading_time_seconds=reading_time_seconds,
            complexity_score=min(complexity_score, 1.0),
            key_points=key_points
        )
    
    def _extract_key_points(self, content: str) -> List[str]:
        """Extract key points from content"""
        key_points = []
        
        # Extract bullet points
        bullet_matches = re.findall(self.summary_patterns["key_points"], content, re.MULTILINE)
        key_points.extend(bullet_matches)
        
        # Extract numbered points
        numbered_matches = re.findall(self.summary_patterns["numbered_points"], content, re.MULTILINE)
        key_points.extend(numbered_matches)
        
        # Extract emphasized text
        important_matches = re.findall(self.summary_patterns["important"], content)
        for match in important_matches:
            for group in match:
                if group:
                    key_points.append(group)
        
        # If no structured points found, extract first few sentences
        if not key_points:
            sentences = re.split(self.summary_patterns["sentences"], content)
            key_points = [s.strip() for s in sentences[:3] if s.strip()]
        
        return key_points[:5]  # Limit to 5 key points
    
    def should_truncate(self, content: str, device_type: DeviceType, content_type: ContentType) -> bool:
        """Check if content should be truncated"""
        limits = self.device_limits.get(device_type, {}).get(content_type)
        if not limits:
            return False
        
        metrics = self.analyze_content(content)
        
        return (
            metrics.character_count > limits.max_characters or
            metrics.word_count > limits.max_words or
            metrics.sentence_count > limits.max_sentences or
            metrics.paragraph_count > limits.max_paragraphs
        )
    
    def truncate_content(self, 
                        content: str, 
                        device_type: DeviceType, 
                        content_type: ContentType,
                        user_id: Optional[str] = None) -> TruncationResult:
        """Truncate content based on device and content type"""
        
        limits = self.device_limits.get(device_type, {}).get(content_type)
        if not limits:
            return TruncationResult(
                original_content=content,
                truncated_content=content,
                is_truncated=False,
                truncation_method="none"
            )
        
        metrics = self.analyze_content(content)
        
        if not self.should_truncate(content, device_type, content_type):
            return TruncationResult(
                original_content=content,
                truncated_content=content,
                is_truncated=False,
                truncation_method="none",
                metrics=metrics
            )
        
        # Choose truncation method
        if limits.truncation_strategy == "smart":
            truncated, method = self._smart_truncate(content, limits)
        elif limits.truncation_strategy == "summary":
            truncated, method = self._summarize_content(content, limits, SummaryLevel.BRIEF)
        else:  # hard truncation
            truncated, method = self._hard_truncate(content, limits)
        
        # Generate continuation token
        continuation_token = self._generate_continuation_token(content, truncated)
        
        # Store full content for continuation
        if continuation_token:
            self._store_content_for_continuation(continuation_token, content, truncated)
        
        return TruncationResult(
            original_content=content,
            truncated_content=truncated,
            is_truncated=True,
            truncation_method=method,
            continuation_token=continuation_token,
            metrics=metrics
        )
    
    def _smart_truncate(self, content: str, limits: LengthLimits) -> Tuple[str, str]:
        """Smart truncation that preserves meaning"""
        paragraphs = content.split('\n\n')
        truncated_paragraphs = []
        current_chars = 0
        current_words = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            para_chars = len(paragraph)
            para_words = len(re.findall(r'\b\w+\b', paragraph))
            
            # Check if adding this paragraph would exceed limits
            if (current_chars + para_chars > limits.max_characters or
                current_words + para_words > limits.max_words or
                len(truncated_paragraphs) >= limits.max_paragraphs):
                
                # Try to fit part of the paragraph
                if len(truncated_paragraphs) < limits.max_paragraphs:
                    remaining_chars = limits.max_characters - current_chars
                    remaining_words = limits.max_words - current_words
                    
                    if remaining_chars > 50 and remaining_words > 10:
                        # Truncate at sentence boundary
                        sentences = re.split(r'([.!?]+\s*)', paragraph)
                        partial_para = ""
                        
                        for i in range(0, len(sentences), 2):
                            if i + 1 < len(sentences):
                                sentence = sentences[i] + sentences[i + 1]
                            else:
                                sentence = sentences[i]
                            
                            if len(partial_para + sentence) <= remaining_chars:
                                partial_para += sentence
                            else:
                                break
                        
                        if partial_para.strip():
                            truncated_paragraphs.append(partial_para.strip() + "...")
                
                break
            
            truncated_paragraphs.append(paragraph)
            current_chars += para_chars + 2  # +2 for paragraph separator
            current_words += para_words
        
        result = '\n\n'.join(truncated_paragraphs)
        if len(result) < len(content):
            result += "\n\n[Content continues...]"
        
        return result, "smart"
    
    def _summarize_content(self, content: str, limits: LengthLimits, level: SummaryLevel) -> Tuple[str, str]:
        """Summarize content to fit limits"""
        key_points = self._extract_key_points(content)
        
        if level == SummaryLevel.BRIEF:
            # Use only top 3 key points
            summary_points = key_points[:3]
        elif level == SummaryLevel.DETAILED:
            # Use top 5 key points with more detail
            summary_points = key_points[:5]
        else:  # COMPREHENSIVE
            # Use all key points
            summary_points = key_points
        
        if not summary_points:
            # Fallback to first few sentences
            sentences = re.split(r'[.!?]+\s*', content)
            summary_points = [s.strip() for s in sentences[:3] if s.strip()]
        
        # Format summary
        if len(summary_points) == 1:
            summary = summary_points[0]
        else:
            summary = "Key points:\n" + "\n".join(f"• {point}" for point in summary_points)
        
        # Ensure summary fits limits
        if len(summary) > limits.max_characters:
            summary = summary[:limits.max_characters - 3] + "..."
        
        return summary, f"summary_{level.value}"
    
    def _hard_truncate(self, content: str, limits: LengthLimits) -> Tuple[str, str]:
        """Hard truncation at character limit"""
        if len(content) <= limits.max_characters:
            return content, "none"
        
        # Truncate at word boundary if possible
        truncated = content[:limits.max_characters]
        last_space = truncated.rfind(' ')
        
        if last_space > limits.max_characters * 0.8:  # If word boundary is not too far back
            truncated = truncated[:last_space]
        
        truncated += "..."
        return truncated, "hard"
    
    def _generate_continuation_token(self, original: str, truncated: str) -> Optional[str]:
        """Generate a token for content continuation"""
        if len(original) <= len(truncated):
            return None
        
        # Create hash of original content
        content_hash = hashlib.md5(original.encode()).hexdigest()
        timestamp = str(int(time.time()))
        
        return f"{content_hash}_{timestamp}"
    
    def _store_content_for_continuation(self, token: str, original: str, truncated: str):
        """Store content for later continuation"""
        self.content_cache[token] = {
            "original": original,
            "truncated": truncated,
            "timestamp": time.time()
        }
        
        # Clean up old entries
        current_time = time.time()
        expired_tokens = [
            t for t, data in self.content_cache.items()
            if current_time - data["timestamp"] > self.cache_ttl
        ]
        
        for token in expired_tokens:
            del self.content_cache[token]
    
    def continue_content(self, continuation_token: str) -> Optional[str]:
        """Get continuation of truncated content"""
        if continuation_token not in self.content_cache:
            return None
        
        data = self.content_cache[continuation_token]
        
        # Check if not expired
        if time.time() - data["timestamp"] > self.cache_ttl:
            del self.content_cache[continuation_token]
            return None
        
        return data["original"]
    
    def set_user_preferences(self, user_id: str, preferences: Dict[str, Any]):
        """Set user-specific content preferences"""
        self.user_preferences[user_id] = preferences
    
    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user-specific content preferences"""
        return self.user_preferences.get(user_id, {})
    
    def get_device_limits(self, device_type: DeviceType, content_type: ContentType) -> Optional[LengthLimits]:
        """Get length limits for device and content type"""
        return self.device_limits.get(device_type, {}).get(content_type)
    
    def update_device_limits(self, device_type: DeviceType, content_type: ContentType, limits: LengthLimits):
        """Update length limits for device and content type"""
        if device_type not in self.device_limits:
            self.device_limits[device_type] = {}
        
        self.device_limits[device_type][content_type] = limits

class AISummarizer:
    """AI-powered content summarization system"""

    def __init__(self):
        self.summarization_prompts = {
            SummaryLevel.BRIEF: """
            Summarize the following content in 1-2 sentences, focusing on the most important information:

            {content}

            Summary:
            """,

            SummaryLevel.DETAILED: """
            Create a detailed summary of the following content in 3-5 sentences, preserving key details:

            {content}

            Summary:
            """,

            SummaryLevel.COMPREHENSIVE: """
            Create a comprehensive summary of the following content, maintaining all important points while being more concise than the original:

            {content}

            Summary:
            """
        }

    async def summarize_with_ai(self,
                               content: str,
                               level: SummaryLevel = SummaryLevel.BRIEF,
                               max_length: Optional[int] = None) -> str:
        """Summarize content using AI model"""
        try:
            # This would integrate with your AI model
            # For now, return a rule-based summary
            return self._rule_based_summary(content, level, max_length)
        except Exception as e:
            logger.error(f"AI summarization failed: {e}")
            return self._rule_based_summary(content, level, max_length)

    def _rule_based_summary(self,
                           content: str,
                           level: SummaryLevel,
                           max_length: Optional[int] = None) -> str:
        """Fallback rule-based summarization"""
        sentences = re.split(r'[.!?]+\s*', content)
        sentences = [s.strip() for s in sentences if s.strip()]

        if level == SummaryLevel.BRIEF:
            summary_sentences = sentences[:2]
        elif level == SummaryLevel.DETAILED:
            summary_sentences = sentences[:5]
        else:  # COMPREHENSIVE
            # Take every other sentence for comprehensive summary
            summary_sentences = sentences[::2]

        summary = '. '.join(summary_sentences)
        if summary and not summary.endswith('.'):
            summary += '.'

        # Apply length limit if specified
        if max_length and len(summary) > max_length:
            summary = summary[:max_length - 3] + "..."

        return summary

class ResponsiveContentFormatter:
    """Responsive content formatter for different devices"""

    def __init__(self):
        self.content_controller = ContentLengthController()
        self.ai_summarizer = AISummarizer()

    def format_for_device(self,
                         content: str,
                         device_type: DeviceType,
                         content_type: ContentType,
                         user_agent: str = "",
                         screen_width: Optional[int] = None,
                         user_preferences: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Format content optimally for specific device"""

        # Detect device if not specified
        if device_type == DeviceType.UNKNOWN:
            device_type = self.content_controller.detect_device_type(user_agent, screen_width)

        # Analyze original content
        metrics = self.content_controller.analyze_content(content)

        # Check if truncation is needed
        needs_truncation = self.content_controller.should_truncate(content, device_type, content_type)

        result = {
            "original_content": content,
            "formatted_content": content,
            "device_type": device_type.value,
            "content_type": content_type.value,
            "is_truncated": False,
            "metrics": {
                "character_count": metrics.character_count,
                "word_count": metrics.word_count,
                "sentence_count": metrics.sentence_count,
                "paragraph_count": metrics.paragraph_count,
                "reading_time_seconds": metrics.reading_time_seconds,
                "complexity_score": metrics.complexity_score
            },
            "device_optimizations": []
        }

        if needs_truncation:
            truncation_result = self.content_controller.truncate_content(
                content, device_type, content_type
            )

            result.update({
                "formatted_content": truncation_result.truncated_content,
                "is_truncated": True,
                "truncation_method": truncation_result.truncation_method,
                "continuation_token": truncation_result.continuation_token
            })

            result["device_optimizations"].append("content_truncated")

        # Apply device-specific formatting
        result["formatted_content"] = self._apply_device_formatting(
            result["formatted_content"], device_type, user_preferences
        )

        return result

    def _apply_device_formatting(self,
                                content: str,
                                device_type: DeviceType,
                                user_preferences: Optional[Dict[str, Any]] = None) -> str:
        """Apply device-specific formatting"""

        if device_type == DeviceType.MOBILE:
            # Mobile optimizations
            # Break long paragraphs
            content = self._break_long_paragraphs(content, max_sentences=3)
            # Add more line breaks for readability
            content = content.replace('. ', '.\n\n')

        elif device_type == DeviceType.SMART_WATCH:
            # Watch optimizations
            # Remove formatting and keep only essential text
            content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # Remove bold
            content = re.sub(r'__(.*?)__', r'\1', content)      # Remove underline
            content = re.sub(r'\n+', ' ', content)              # Single line

        elif device_type == DeviceType.SMART_TV:
            # TV optimizations
            # Larger text blocks, fewer line breaks
            content = content.replace('\n\n', '\n')

        return content

    def _break_long_paragraphs(self, content: str, max_sentences: int = 3) -> str:
        """Break long paragraphs into smaller ones"""
        paragraphs = content.split('\n\n')
        result_paragraphs = []

        for paragraph in paragraphs:
            sentences = re.split(r'([.!?]+\s*)', paragraph)
            current_para = ""
            sentence_count = 0

            for i in range(0, len(sentences), 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i] + sentences[i + 1]
                else:
                    sentence = sentences[i]

                current_para += sentence
                sentence_count += 1

                if sentence_count >= max_sentences and i + 2 < len(sentences):
                    result_paragraphs.append(current_para.strip())
                    current_para = ""
                    sentence_count = 0

            if current_para.strip():
                result_paragraphs.append(current_para.strip())

        return '\n\n'.join(result_paragraphs)

# Global instances
content_length_controller = ContentLengthController()
ai_summarizer = AISummarizer()
responsive_formatter = ResponsiveContentFormatter()
