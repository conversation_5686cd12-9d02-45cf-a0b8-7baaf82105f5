# app/core/async_task_manager.py
import asyncio
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Callable, Union, Coroutine
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import traceback
import json

logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class TaskResult:
    """Result of task execution"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    execution_time: Optional[float] = None
    retry_count: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class TaskConfig:
    """Configuration for task execution"""
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff_factor: float = 2.0
    timeout: Optional[float] = None
    priority: TaskPriority = TaskPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)

class AsyncTask:
    """Represents an asynchronous task"""
    
    def __init__(self, 
                 task_id: str,
                 func: Union[Callable, Coroutine],
                 args: tuple = (),
                 kwargs: Dict[str, Any] = None,
                 config: TaskConfig = None):
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        self.config = config or TaskConfig()
        self.created_at = time.time()
        self.status = TaskStatus.PENDING
        self.result: Optional[TaskResult] = None
        self._future: Optional[asyncio.Future] = None
    
    def __lt__(self, other):
        """For priority queue ordering"""
        return self.config.priority.value > other.config.priority.value

class AsyncTaskManager:
    """Advanced asynchronous task manager with queuing and monitoring"""
    
    def __init__(self, 
                 max_concurrent_tasks: int = 10,
                 max_queue_size: int = 1000,
                 worker_pool_size: int = 4):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_queue_size = max_queue_size
        self.worker_pool_size = worker_pool_size
        
        # Task storage
        self.tasks: Dict[str, AsyncTask] = {}
        self.task_queue = asyncio.PriorityQueue(maxsize=max_queue_size)
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # Worker management
        self.workers: List[asyncio.Task] = []
        self.thread_pool = ThreadPoolExecutor(max_workers=worker_pool_size)
        self._shutdown = False
        
        # Monitoring
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "cancelled_tasks": 0,
            "avg_execution_time": 0.0,
            "queue_size": 0,
            "running_tasks": 0
        }
        
        # Dead letter queue for failed tasks
        self.dead_letter_queue: List[AsyncTask] = []
        self.max_dead_letter_size = 100
    
    async def start(self):
        """Start the task manager and workers"""
        if self.workers:
            logger.warning("Task manager already started")
            return
        
        # Start worker tasks
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"Started async task manager with {len(self.workers)} workers")
    
    async def stop(self):
        """Stop the task manager and cleanup"""
        self._shutdown = True
        
        # Cancel all running tasks
        for task_id, task in self.running_tasks.items():
            task.cancel()
            logger.info(f"Cancelled running task: {task_id}")
        
        # Wait for workers to finish
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
            self.workers.clear()
        
        # Shutdown thread pool
        self.thread_pool.shutdown(wait=True)
        
        logger.info("Async task manager stopped")
    
    async def _worker(self, worker_name: str):
        """Worker coroutine that processes tasks from the queue"""
        logger.info(f"Worker {worker_name} started")
        
        while not self._shutdown:
            try:
                # Get task from queue with timeout
                try:
                    priority, task = await asyncio.wait_for(
                        self.task_queue.get(), timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Execute the task
                await self._execute_task(task, worker_name)
                
                # Mark task as done in queue
                self.task_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
        
        logger.info(f"Worker {worker_name} stopped")
    
    async def _execute_task(self, task: AsyncTask, worker_name: str):
        """Execute a single task with error handling and retries"""
        task.status = TaskStatus.RUNNING
        start_time = time.time()
        
        logger.debug(f"Worker {worker_name} executing task {task.task_id}")
        
        try:
            # Create task future
            if asyncio.iscoroutinefunction(task.func):
                future = asyncio.create_task(task.func(*task.args, **task.kwargs))
            elif asyncio.iscoroutine(task.func):
                future = asyncio.create_task(task.func)
            else:
                # Run in thread pool for sync functions
                future = asyncio.create_task(
                    asyncio.get_event_loop().run_in_executor(
                        self.thread_pool, task.func, *task.args
                    )
                )
            
            # Store running task
            self.running_tasks[task.task_id] = future
            
            # Execute with timeout if specified
            if task.config.timeout:
                result = await asyncio.wait_for(future, timeout=task.config.timeout)
            else:
                result = await future
            
            # Task completed successfully
            end_time = time.time()
            execution_time = end_time - start_time
            
            task.status = TaskStatus.COMPLETED
            task.result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                retry_count=task.result.retry_count if task.result else 0,
                metadata=task.config.metadata
            )
            
            # Update stats
            self._update_stats(task, execution_time, True)
            
            logger.debug(f"Task {task.task_id} completed in {execution_time:.2f}s")
            
        except asyncio.CancelledError:
            task.status = TaskStatus.CANCELLED
            task.result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.CANCELLED,
                error="Task was cancelled",
                start_time=start_time,
                end_time=time.time()
            )
            self.stats["cancelled_tasks"] += 1
            logger.info(f"Task {task.task_id} was cancelled")
            
        except Exception as e:
            # Handle task failure with retry logic
            await self._handle_task_failure(task, e, start_time)
            
        finally:
            # Cleanup
            if task.task_id in self.running_tasks:
                del self.running_tasks[task.task_id]
    
    async def _handle_task_failure(self, task: AsyncTask, error: Exception, start_time: float):
        """Handle task failure with retry logic"""
        retry_count = task.result.retry_count if task.result else 0
        
        if retry_count < task.config.max_retries:
            # Retry the task
            retry_count += 1
            task.status = TaskStatus.RETRYING
            
            # Calculate retry delay with backoff
            delay = task.config.retry_delay * (task.config.retry_backoff_factor ** (retry_count - 1))
            
            logger.warning(f"Task {task.task_id} failed (attempt {retry_count}), retrying in {delay:.2f}s: {error}")
            
            # Create new task result for retry
            task.result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.RETRYING,
                error=str(error),
                retry_count=retry_count,
                metadata=task.config.metadata
            )
            
            # Schedule retry
            await asyncio.sleep(delay)
            await self.task_queue.put((task.config.priority.value, task))
            
        else:
            # Max retries exceeded, mark as failed
            end_time = time.time()
            execution_time = end_time - start_time
            
            task.status = TaskStatus.FAILED
            task.result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.FAILED,
                error=str(error),
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time,
                retry_count=retry_count,
                metadata=task.config.metadata
            )
            
            # Add to dead letter queue
            self._add_to_dead_letter_queue(task)
            
            # Update stats
            self._update_stats(task, execution_time, False)
            
            logger.error(f"Task {task.task_id} failed after {retry_count} retries: {error}")
            logger.debug(f"Task failure traceback: {traceback.format_exc()}")
    
    def _add_to_dead_letter_queue(self, task: AsyncTask):
        """Add failed task to dead letter queue"""
        self.dead_letter_queue.append(task)
        
        # Limit dead letter queue size
        if len(self.dead_letter_queue) > self.max_dead_letter_size:
            self.dead_letter_queue.pop(0)
    
    def _update_stats(self, task: AsyncTask, execution_time: float, success: bool):
        """Update task execution statistics"""
        self.stats["total_tasks"] += 1
        
        if success:
            self.stats["completed_tasks"] += 1
        else:
            self.stats["failed_tasks"] += 1
        
        # Update average execution time
        total_completed = self.stats["completed_tasks"]
        if total_completed > 0:
            current_avg = self.stats["avg_execution_time"]
            self.stats["avg_execution_time"] = (
                (current_avg * (total_completed - 1) + execution_time) / total_completed
            )
        
        # Update current counts
        self.stats["queue_size"] = self.task_queue.qsize()
        self.stats["running_tasks"] = len(self.running_tasks)
    
    async def submit_task(self, 
                         func: Union[Callable, Coroutine],
                         args: tuple = (),
                         kwargs: Dict[str, Any] = None,
                         config: TaskConfig = None,
                         task_id: Optional[str] = None) -> str:
        """Submit a task for execution"""
        if self._shutdown:
            raise RuntimeError("Task manager is shutting down")
        
        task_id = task_id or str(uuid.uuid4())
        task = AsyncTask(task_id, func, args, kwargs or {}, config or TaskConfig())
        
        # Store task
        self.tasks[task_id] = task
        
        # Add to queue
        try:
            await self.task_queue.put((task.config.priority.value, task))
            logger.debug(f"Task {task_id} submitted to queue")
            return task_id
        except asyncio.QueueFull:
            raise RuntimeError("Task queue is full")
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """Get result of a task"""
        task = self.tasks.get(task_id)
        return task.result if task else None
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """Get status of a task"""
        task = self.tasks.get(task_id)
        return task.status if task else None
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> TaskResult:
        """Wait for a task to complete"""
        start_time = time.time()
        
        while True:
            task = self.tasks.get(task_id)
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return task.result
            
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {task_id} did not complete within {timeout}s")
            
            await asyncio.sleep(0.1)
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            return True
        
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current task manager statistics"""
        stats = self.stats.copy()
        stats.update({
            "queue_size": self.task_queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "dead_letter_queue_size": len(self.dead_letter_queue),
            "total_stored_tasks": len(self.tasks)
        })
        return stats
    
    def get_dead_letter_tasks(self) -> List[Dict[str, Any]]:
        """Get tasks in dead letter queue"""
        return [
            {
                "task_id": task.task_id,
                "error": task.result.error if task.result else None,
                "retry_count": task.result.retry_count if task.result else 0,
                "created_at": task.created_at,
                "metadata": task.config.metadata
            }
            for task in self.dead_letter_queue
        ]
    
    async def cleanup_completed_tasks(self, max_age: float = 3600):
        """Clean up old completed tasks"""
        current_time = time.time()
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                task.result and task.result.end_time and
                (current_time - task.result.end_time) > max_age):
                to_remove.append(task_id)
        
        for task_id in to_remove:
            del self.tasks[task_id]
        
        if to_remove:
            logger.info(f"Cleaned up {len(to_remove)} old completed tasks")

# Global task manager instance
task_manager = AsyncTaskManager()
