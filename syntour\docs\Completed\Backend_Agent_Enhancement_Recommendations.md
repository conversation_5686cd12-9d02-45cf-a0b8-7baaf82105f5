# SynTour 后端Agent功能增强建议

## 概述
基于对SynTour AI Agent架构的深入分析，本文档提供决策逻辑、任务调度、状态管理和工作流程的全面优化建议。

## 1. 当前Agent架构分析

### 1.1 现有架构问题
**问题识别:**
```python
# main.py 中的问题
gemini_model = None  # 全局变量，缺乏状态管理
continuation_store: Dict[str, Dict[str, Any]] = {}  # 简单字典存储，无持久化
```

**架构缺陷:**
- 缺乏统一的Agent状态管理
- 对话上下文管理简陋
- 没有任务优先级和调度机制
- 缺乏Agent间协作能力

### 1.2 Agent能力分析
**当前能力:**
- 基础对话处理
- 多模态内容处理
- 旅游规划专业知识
- 简单的上下文延续

**缺失能力:**
- 复杂任务分解
- 多步骤工作流管理
- 智能决策树
- 学习和适应能力

## 2. Agent架构重构建议

### 2.1 分层Agent架构
**优化建议:**
```python
# 新建 app/core/agent_architecture.py
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import asyncio
import uuid

class AgentType(Enum):
    COORDINATOR = "coordinator"      # 协调器Agent
    SPECIALIST = "specialist"        # 专业Agent
    EXECUTOR = "executor"           # 执行Agent
    MONITOR = "monitor"             # 监控Agent

class TaskPriority(Enum):
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4

@dataclass
class AgentTask:
    id: str
    type: str
    priority: TaskPriority
    payload: Dict[str, Any]
    dependencies: List[str] = None
    timeout: int = 300  # 5分钟默认超时
    retry_count: int = 0
    max_retries: int = 3
    created_at: float = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    status: str = "pending"  # pending, running, completed, failed

class BaseAgent(ABC):
    def __init__(self, agent_id: str, agent_type: AgentType):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.status = "idle"
        self.current_task: Optional[AgentTask] = None
        self.capabilities: List[str] = []
        self.performance_metrics = {
            "tasks_completed": 0,
            "tasks_failed": 0,
            "avg_response_time": 0.0,
            "success_rate": 1.0
        }
    
    @abstractmethod
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """处理任务的抽象方法"""
        pass
    
    @abstractmethod
    async def can_handle_task(self, task: AgentTask) -> bool:
        """检查是否能处理特定任务"""
        pass
    
    async def execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """执行任务的通用方法"""
        self.status = "busy"
        self.current_task = task
        task.status = "running"
        task.started_at = time.time()
        
        try:
            result = await self.process_task(task)
            task.status = "completed"
            task.completed_at = time.time()
            self.performance_metrics["tasks_completed"] += 1
            return result
        except Exception as e:
            task.status = "failed"
            self.performance_metrics["tasks_failed"] += 1
            raise e
        finally:
            self.status = "idle"
            self.current_task = None
```

### 2.2 专业化Agent实现
**优化建议:**
```python
# 新建 app/agents/travel_planning_agent.py
class TravelPlanningAgent(BaseAgent):
    def __init__(self):
        super().__init__("travel_planner", AgentType.SPECIALIST)
        self.capabilities = [
            "itinerary_planning",
            "destination_recommendation",
            "budget_optimization",
            "activity_suggestion"
        ]
        self.knowledge_base = {
            "destinations": {},
            "activities": {},
            "transportation": {},
            "accommodations": {}
        }
    
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """处理旅游规划任务"""
        task_type = task.payload.get("type")
        
        if task_type == "create_itinerary":
            return await self._create_itinerary(task.payload)
        elif task_type == "optimize_budget":
            return await self._optimize_budget(task.payload)
        elif task_type == "suggest_activities":
            return await self._suggest_activities(task.payload)
        else:
            raise ValueError(f"Unknown task type: {task_type}")
    
    async def _create_itinerary(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """创建行程规划"""
        destination = payload.get("destination")
        duration = payload.get("duration")
        budget = payload.get("budget")
        interests = payload.get("interests", [])
        
        # 分解任务为子任务
        subtasks = [
            {"type": "research_destination", "destination": destination},
            {"type": "find_accommodations", "destination": destination, "budget": budget},
            {"type": "plan_activities", "destination": destination, "interests": interests},
            {"type": "optimize_schedule", "duration": duration}
        ]
        
        results = []
        for subtask in subtasks:
            result = await self._execute_subtask(subtask)
            results.append(result)
        
        return {
            "itinerary": self._compile_itinerary(results),
            "total_cost": self._calculate_total_cost(results),
            "recommendations": self._generate_recommendations(results)
        }

# 新建 app/agents/api_coordination_agent.py
class APICoordinationAgent(BaseAgent):
    def __init__(self, api_clients: Dict[str, Any]):
        super().__init__("api_coordinator", AgentType.COORDINATOR)
        self.api_clients = api_clients
        self.capabilities = [
            "api_orchestration",
            "data_aggregation",
            "fallback_handling",
            "response_optimization"
        ]
    
    async def process_task(self, task: AgentTask) -> Dict[str, Any]:
        """协调多个API调用"""
        api_calls = task.payload.get("api_calls", [])
        
        # 并行执行API调用
        tasks = []
        for api_call in api_calls:
            api_task = asyncio.create_task(
                self._execute_api_call(api_call)
            )
            tasks.append(api_task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = {}
        for i, result in enumerate(results):
            api_name = api_calls[i]["api_name"]
            if isinstance(result, Exception):
                processed_results[api_name] = {
                    "success": False,
                    "error": str(result),
                    "fallback_data": await self._get_fallback_data(api_name)
                }
            else:
                processed_results[api_name] = {
                    "success": True,
                    "data": result
                }
        
        return {
            "aggregated_data": self._aggregate_api_responses(processed_results),
            "api_status": processed_results
        }
```

## 3. 智能决策系统

### 3.1 决策树实现
**优化建议:**
```python
# 新建 app/core/decision_engine.py
from typing import Dict, Any, Callable, List
import json

class DecisionNode:
    def __init__(
        self,
        condition: Callable[[Dict[str, Any]], bool],
        action: Optional[Callable] = None,
        children: Optional[Dict[str, 'DecisionNode']] = None
    ):
        self.condition = condition
        self.action = action
        self.children = children or {}
    
    async def evaluate(self, context: Dict[str, Any]) -> Any:
        """评估决策节点"""
        if self.condition(context):
            if self.action:
                return await self.action(context)
            
            # 评估子节点
            for key, child in self.children.items():
                result = await child.evaluate(context)
                if result is not None:
                    return result
        
        return None

class TravelDecisionEngine:
    def __init__(self):
        self.decision_tree = self._build_decision_tree()
    
    def _build_decision_tree(self) -> DecisionNode:
        """构建旅游决策树"""
        # 根节点：确定请求类型
        root = DecisionNode(
            condition=lambda ctx: True,
            children={
                "planning": DecisionNode(
                    condition=lambda ctx: "plan" in ctx.get("intent", "").lower(),
                    children={
                        "budget_planning": DecisionNode(
                            condition=lambda ctx: ctx.get("budget") is not None,
                            action=self._handle_budget_planning
                        ),
                        "destination_planning": DecisionNode(
                            condition=lambda ctx: ctx.get("destination") is not None,
                            action=self._handle_destination_planning
                        ),
                        "general_planning": DecisionNode(
                            condition=lambda ctx: True,
                            action=self._handle_general_planning
                        )
                    }
                ),
                "information": DecisionNode(
                    condition=lambda ctx: any(word in ctx.get("intent", "").lower() 
                                            for word in ["info", "tell", "about"]),
                    action=self._handle_information_request
                ),
                "booking": DecisionNode(
                    condition=lambda ctx: "book" in ctx.get("intent", "").lower(),
                    action=self._handle_booking_request
                )
            }
        )
        return root
    
    async def make_decision(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """基于用户输入和上下文做出决策"""
        # 分析用户意图
        intent = await self._analyze_intent(user_input)
        context["intent"] = intent
        context["user_input"] = user_input
        
        # 执行决策树
        result = await self.decision_tree.evaluate(context)
        
        return result or {"action": "default_response", "message": "I'm not sure how to help with that."}
```

## 4. 任务调度和状态管理

### 4.1 任务调度器
**优化建议:**
```python
# 新建 app/core/task_scheduler.py
import heapq
import asyncio
from typing import Dict, List, Optional
from datetime import datetime, timedelta

class TaskScheduler:
    def __init__(self):
        self.task_queue = []  # 优先级队列
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.completed_tasks: Dict[str, AgentTask] = {}
        self.failed_tasks: Dict[str, AgentTask] = {}
        self.agents: Dict[str, BaseAgent] = {}
        self.max_concurrent_tasks = 10
    
    def register_agent(self, agent: BaseAgent):
        """注册Agent"""
        self.agents[agent.agent_id] = agent
    
    async def schedule_task(self, task: AgentTask) -> str:
        """调度任务"""
        # 添加到优先级队列
        heapq.heappush(self.task_queue, (-task.priority.value, task.created_at, task))
        
        # 尝试立即执行
        await self._try_execute_tasks()
        
        return task.id
    
    async def _try_execute_tasks(self):
        """尝试执行队列中的任务"""
        while (len(self.running_tasks) < self.max_concurrent_tasks and 
               self.task_queue):
            
            _, _, task = heapq.heappop(self.task_queue)
            
            # 找到合适的Agent
            suitable_agent = await self._find_suitable_agent(task)
            if suitable_agent:
                # 创建异步任务
                async_task = asyncio.create_task(
                    self._execute_task_with_agent(task, suitable_agent)
                )
                self.running_tasks[task.id] = async_task
    
    async def _find_suitable_agent(self, task: AgentTask) -> Optional[BaseAgent]:
        """找到合适的Agent执行任务"""
        available_agents = [
            agent for agent in self.agents.values()
            if agent.status == "idle"
        ]
        
        for agent in available_agents:
            if await agent.can_handle_task(task):
                return agent
        
        return None
    
    async def _execute_task_with_agent(self, task: AgentTask, agent: BaseAgent):
        """使用指定Agent执行任务"""
        try:
            result = await agent.execute_task(task)
            self.completed_tasks[task.id] = task
            return result
        except Exception as e:
            task.retry_count += 1
            if task.retry_count < task.max_retries:
                # 重新调度任务
                await self.schedule_task(task)
            else:
                self.failed_tasks[task.id] = task
            raise e
        finally:
            self.running_tasks.pop(task.id, None)
            await self._try_execute_tasks()  # 尝试执行下一个任务
```

### 4.2 状态管理系统
**优化建议:**
```python
# 新建 app/core/state_manager.py
import json
import asyncio
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

class ConversationState:
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.context: Dict[str, Any] = {}
        self.message_history: List[Dict[str, Any]] = []
        self.user_preferences: Dict[str, Any] = {}
        self.current_task: Optional[str] = None
        self.task_progress: Dict[str, Any] = {}
    
    def update_activity(self):
        """更新最后活动时间"""
        self.last_activity = datetime.now()
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """添加消息到历史记录"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        self.message_history.append(message)
        self.update_activity()
    
    def get_context_summary(self) -> str:
        """获取上下文摘要"""
        recent_messages = self.message_history[-5:]  # 最近5条消息
        summary_parts = []
        
        for msg in recent_messages:
            summary_parts.append(f"{msg['role']}: {msg['content'][:100]}...")
        
        return "\n".join(summary_parts)

class StateManager:
    def __init__(self):
        self.sessions: Dict[str, ConversationState] = {}
        self.session_timeout = timedelta(hours=2)
        self._cleanup_task = None
    
    async def start(self):
        """启动状态管理器"""
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def stop(self):
        """停止状态管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
    
    def get_or_create_session(self, session_id: str) -> ConversationState:
        """获取或创建会话状态"""
        if session_id not in self.sessions:
            self.sessions[session_id] = ConversationState(session_id)
        else:
            self.sessions[session_id].update_activity()
        
        return self.sessions[session_id]
    
    async def _periodic_cleanup(self):
        """定期清理过期会话"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时清理一次
                now = datetime.now()
                expired_sessions = [
                    session_id for session_id, state in self.sessions.items()
                    if now - state.last_activity > self.session_timeout
                ]
                
                for session_id in expired_sessions:
                    del self.sessions[session_id]
                
                logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")
```

## 5. Agent协作和工作流

### 5.1 工作流引擎
**优化建议:**
```python
# 新建 app/core/workflow_engine.py
from typing import Dict, List, Any, Callable
from enum import Enum

class WorkflowStepType(Enum):
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    LOOP = "loop"

class WorkflowStep:
    def __init__(
        self,
        step_id: str,
        step_type: WorkflowStepType,
        agent_type: str,
        action: str,
        parameters: Dict[str, Any] = None,
        condition: Optional[Callable] = None,
        next_steps: List[str] = None
    ):
        self.step_id = step_id
        self.step_type = step_type
        self.agent_type = agent_type
        self.action = action
        self.parameters = parameters or {}
        self.condition = condition
        self.next_steps = next_steps or []

class TravelPlanningWorkflow:
    def __init__(self, scheduler: TaskScheduler):
        self.scheduler = scheduler
        self.workflows = self._define_workflows()
    
    def _define_workflows(self) -> Dict[str, List[WorkflowStep]]:
        """定义旅游规划工作流"""
        return {
            "comprehensive_travel_planning": [
                WorkflowStep(
                    "analyze_request",
                    WorkflowStepType.SEQUENTIAL,
                    "travel_planner",
                    "analyze_travel_request",
                    next_steps=["gather_destination_info", "check_budget"]
                ),
                WorkflowStep(
                    "gather_destination_info",
                    WorkflowStepType.PARALLEL,
                    "api_coordinator",
                    "gather_destination_data",
                    parameters={"apis": ["geoapify", "google_places"]},
                    next_steps=["create_itinerary"]
                ),
                WorkflowStep(
                    "check_budget",
                    WorkflowStepType.PARALLEL,
                    "api_coordinator",
                    "check_prices",
                    parameters={"apis": ["amadeus", "hotelbeds"]},
                    next_steps=["create_itinerary"]
                ),
                WorkflowStep(
                    "create_itinerary",
                    WorkflowStepType.SEQUENTIAL,
                    "travel_planner",
                    "create_detailed_itinerary",
                    next_steps=["optimize_plan"]
                ),
                WorkflowStep(
                    "optimize_plan",
                    WorkflowStepType.CONDITIONAL,
                    "travel_planner",
                    "optimize_itinerary",
                    condition=lambda ctx: ctx.get("optimization_needed", False),
                    next_steps=["finalize_plan"]
                ),
                WorkflowStep(
                    "finalize_plan",
                    WorkflowStepType.SEQUENTIAL,
                    "travel_planner",
                    "finalize_travel_plan"
                )
            ]
        }
    
    async def execute_workflow(
        self,
        workflow_name: str,
        initial_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行工作流"""
        workflow_steps = self.workflows.get(workflow_name)
        if not workflow_steps:
            raise ValueError(f"Unknown workflow: {workflow_name}")
        
        context = initial_context.copy()
        current_steps = [workflow_steps[0]]  # 从第一步开始
        
        while current_steps:
            next_steps = []
            
            if current_steps[0].step_type == WorkflowStepType.PARALLEL:
                # 并行执行
                tasks = []
                for step in current_steps:
                    task = self._create_task_from_step(step, context)
                    tasks.append(self.scheduler.schedule_task(task))
                
                # 等待所有任务完成
                results = await asyncio.gather(*tasks)
                for result in results:
                    context.update(result)
            
            else:
                # 顺序执行
                for step in current_steps:
                    if step.condition and not step.condition(context):
                        continue
                    
                    task = self._create_task_from_step(step, context)
                    result = await self.scheduler.schedule_task(task)
                    context.update(result)
            
            # 确定下一步
            for step in current_steps:
                for next_step_id in step.next_steps:
                    next_step = next(
                        (s for s in workflow_steps if s.step_id == next_step_id),
                        None
                    )
                    if next_step:
                        next_steps.append(next_step)
            
            current_steps = next_steps
        
        return context
```

## 6. 实施优先级和时间表

### 高优先级 (立即实施)
1. **Agent架构重构** - 2周
2. **状态管理系统** - 1周
3. **任务调度器** - 1周

### 中优先级 (2-4周内)
1. **决策引擎** - 2周
2. **专业化Agent** - 2周
3. **工作流引擎** - 3周

### 低优先级 (长期优化)
1. **高级协作机制** - 4周
2. **学习和适应能力** - 6周
3. **性能优化** - 持续进行

## 总结

这些Agent功能增强建议将显著提升SynTour AI系统的智能化水平、处理能力和用户体验。建议按照优先级逐步实施，并在每个阶段进行充分的测试和验证。
