# app/core/health_monitor.py
import asyncio
import logging
import time
import psutil
import os
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """Health status levels"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check configuration"""
    name: str
    check_func: Callable
    timeout: float = 10.0
    interval: float = 30.0
    critical: bool = False
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class HealthResult:
    """Result of a health check"""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    response_time: float = 0.0
    error: Optional[str] = None

@dataclass
class SystemMetrics:
    """System performance metrics"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    process_count: int
    uptime: float
    load_average: Optional[List[float]] = None

class HealthMonitor:
    """Comprehensive health monitoring system"""
    
    def __init__(self):
        self.health_checks: Dict[str, HealthCheck] = {}
        self.health_results: Dict[str, HealthResult] = {}
        self.health_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
        
        # Monitoring tasks
        self.monitor_tasks: Dict[str, asyncio.Task] = {}
        self._shutdown = False
        
        # System metrics
        self.system_metrics: Optional[SystemMetrics] = None
        self.metrics_history: List[SystemMetrics] = []
        self.max_metrics_history = 100
        
        # Alerting
        self.alert_handlers: List[Callable] = []
        self.alert_thresholds = {
            "cpu_percent": 80.0,
            "memory_percent": 85.0,
            "disk_percent": 90.0,
            "response_time": 5.0
        }
        
        # Setup default health checks
        self._setup_default_checks()
    
    def _setup_default_checks(self):
        """Setup default health checks"""
        
        # System health check
        self.add_health_check(HealthCheck(
            name="system",
            check_func=self._check_system_health,
            interval=30.0,
            critical=True
        ))
        
        # Memory health check
        self.add_health_check(HealthCheck(
            name="memory",
            check_func=self._check_memory_health,
            interval=30.0,
            critical=True
        ))
        
        # Disk health check
        self.add_health_check(HealthCheck(
            name="disk",
            check_func=self._check_disk_health,
            interval=60.0,
            critical=False
        ))
    
    def add_health_check(self, health_check: HealthCheck):
        """Add a new health check"""
        self.health_checks[health_check.name] = health_check
        logger.info(f"Added health check: {health_check.name}")
    
    def remove_health_check(self, name: str):
        """Remove a health check"""
        if name in self.health_checks:
            del self.health_checks[name]
            if name in self.monitor_tasks:
                self.monitor_tasks[name].cancel()
                del self.monitor_tasks[name]
            logger.info(f"Removed health check: {name}")
    
    def add_alert_handler(self, handler: Callable):
        """Add an alert handler"""
        self.alert_handlers.append(handler)
    
    async def start_monitoring(self):
        """Start all health check monitoring tasks"""
        if self.monitor_tasks:
            logger.warning("Health monitoring already started")
            return
        
        for name, health_check in self.health_checks.items():
            if health_check.enabled:
                task = asyncio.create_task(self._monitor_health_check(health_check))
                self.monitor_tasks[name] = task
        
        # Start system metrics collection
        metrics_task = asyncio.create_task(self._collect_system_metrics())
        self.monitor_tasks["system_metrics"] = metrics_task
        
        logger.info(f"Started health monitoring with {len(self.monitor_tasks)} checks")
    
    async def stop_monitoring(self):
        """Stop all health check monitoring tasks"""
        self._shutdown = True
        
        for name, task in self.monitor_tasks.items():
            task.cancel()
            logger.info(f"Cancelled health check: {name}")
        
        # Wait for all tasks to complete
        if self.monitor_tasks:
            await asyncio.gather(*self.monitor_tasks.values(), return_exceptions=True)
            self.monitor_tasks.clear()
        
        logger.info("Health monitoring stopped")
    
    async def _monitor_health_check(self, health_check: HealthCheck):
        """Monitor a single health check"""
        logger.info(f"Started monitoring health check: {health_check.name}")
        
        while not self._shutdown:
            try:
                result = await self._execute_health_check(health_check)
                self.health_results[health_check.name] = result
                
                # Add to history
                self._add_to_history(result)
                
                # Check for alerts
                await self._check_alerts(result, health_check)
                
                # Wait for next check
                await asyncio.sleep(health_check.interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check {health_check.name}: {e}")
                
                # Create error result
                error_result = HealthResult(
                    name=health_check.name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {str(e)}",
                    error=str(e)
                )
                self.health_results[health_check.name] = error_result
                
                await asyncio.sleep(health_check.interval)
        
        logger.info(f"Stopped monitoring health check: {health_check.name}")
    
    async def _execute_health_check(self, health_check: HealthCheck) -> HealthResult:
        """Execute a single health check"""
        start_time = time.time()
        
        try:
            # Execute with timeout
            if asyncio.iscoroutinefunction(health_check.check_func):
                result = await asyncio.wait_for(
                    health_check.check_func(), 
                    timeout=health_check.timeout
                )
            else:
                result = health_check.check_func()
            
            response_time = time.time() - start_time
            
            # Parse result
            if isinstance(result, dict):
                status = HealthStatus(result.get('status', 'healthy'))
                message = result.get('message', 'OK')
                details = result.get('details', {})
            else:
                status = HealthStatus.HEALTHY
                message = str(result) if result else 'OK'
                details = {}
            
            return HealthResult(
                name=health_check.name,
                status=status,
                message=message,
                details=details,
                response_time=response_time
            )
            
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return HealthResult(
                name=health_check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check timed out after {health_check.timeout}s",
                response_time=response_time,
                error="timeout"
            )
        
        except Exception as e:
            response_time = time.time() - start_time
            return HealthResult(
                name=health_check.name,
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                response_time=response_time,
                error=str(e)
            )
    
    def _add_to_history(self, result: HealthResult):
        """Add health result to history"""
        history_entry = {
            "name": result.name,
            "status": result.status.value,
            "message": result.message,
            "timestamp": result.timestamp.isoformat(),
            "response_time": result.response_time
        }
        
        self.health_history.append(history_entry)
        
        # Limit history size
        if len(self.health_history) > self.max_history_size:
            self.health_history.pop(0)
    
    async def _check_alerts(self, result: HealthResult, health_check: HealthCheck):
        """Check if alerts should be sent"""
        should_alert = False
        alert_reason = ""
        
        # Check status-based alerts
        if result.status == HealthStatus.UNHEALTHY and health_check.critical:
            should_alert = True
            alert_reason = f"Critical health check {result.name} is unhealthy: {result.message}"
        
        # Check response time alerts
        if result.response_time > self.alert_thresholds.get("response_time", 5.0):
            should_alert = True
            alert_reason = f"Health check {result.name} response time ({result.response_time:.2f}s) exceeded threshold"
        
        if should_alert:
            await self._send_alert({
                "type": "health_check_alert",
                "check_name": result.name,
                "status": result.status.value,
                "message": result.message,
                "response_time": result.response_time,
                "timestamp": result.timestamp.isoformat(),
                "reason": alert_reason
            })
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        logger.info("Started system metrics collection")
        
        while not self._shutdown:
            try:
                # Collect metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                network = psutil.net_io_counters()
                
                # Get load average (Unix only)
                load_avg = None
                try:
                    load_avg = list(os.getloadavg())
                except (OSError, AttributeError):
                    pass  # Windows doesn't have getloadavg
                
                metrics = SystemMetrics(
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    disk_percent=disk.percent,
                    network_io={
                        "bytes_sent": network.bytes_sent,
                        "bytes_recv": network.bytes_recv,
                        "packets_sent": network.packets_sent,
                        "packets_recv": network.packets_recv
                    },
                    process_count=len(psutil.pids()),
                    uptime=time.time() - psutil.boot_time(),
                    load_average=load_avg
                )
                
                self.system_metrics = metrics
                self.metrics_history.append(metrics)
                
                # Limit history size
                if len(self.metrics_history) > self.max_metrics_history:
                    self.metrics_history.pop(0)
                
                # Check for system alerts
                await self._check_system_alerts(metrics)
                
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(30)
        
        logger.info("Stopped system metrics collection")
    
    async def _check_system_alerts(self, metrics: SystemMetrics):
        """Check system metrics for alert conditions"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds["cpu_percent"]:
            alerts.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.alert_thresholds["memory_percent"]:
            alerts.append(f"High memory usage: {metrics.memory_percent:.1f}%")
        
        if metrics.disk_percent > self.alert_thresholds["disk_percent"]:
            alerts.append(f"High disk usage: {metrics.disk_percent:.1f}%")
        
        for alert in alerts:
            await self._send_alert({
                "type": "system_alert",
                "message": alert,
                "metrics": {
                    "cpu_percent": metrics.cpu_percent,
                    "memory_percent": metrics.memory_percent,
                    "disk_percent": metrics.disk_percent
                },
                "timestamp": datetime.now().isoformat()
            })
    
    async def _send_alert(self, alert_data: Dict[str, Any]):
        """Send alert to all handlers"""
        for handler in self.alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert_data)
                else:
                    handler(alert_data)
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")
    
    # Default health check functions
    async def _check_system_health(self) -> Dict[str, Any]:
        """Check overall system health"""
        if not self.system_metrics:
            return {"status": "unknown", "message": "No system metrics available"}
        
        metrics = self.system_metrics
        issues = []
        
        if metrics.cpu_percent > 90:
            issues.append(f"High CPU: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > 90:
            issues.append(f"High memory: {metrics.memory_percent:.1f}%")
        
        if issues:
            return {
                "status": "degraded",
                "message": f"System issues detected: {', '.join(issues)}",
                "details": {
                    "cpu_percent": metrics.cpu_percent,
                    "memory_percent": metrics.memory_percent,
                    "disk_percent": metrics.disk_percent
                }
            }
        
        return {
            "status": "healthy",
            "message": "System operating normally",
            "details": {
                "cpu_percent": metrics.cpu_percent,
                "memory_percent": metrics.memory_percent,
                "disk_percent": metrics.disk_percent
            }
        }
    
    async def _check_memory_health(self) -> Dict[str, Any]:
        """Check memory health"""
        memory = psutil.virtual_memory()
        
        if memory.percent > 95:
            return {
                "status": "unhealthy",
                "message": f"Critical memory usage: {memory.percent:.1f}%",
                "details": {"memory_percent": memory.percent, "available_gb": memory.available / (1024**3)}
            }
        elif memory.percent > 85:
            return {
                "status": "degraded",
                "message": f"High memory usage: {memory.percent:.1f}%",
                "details": {"memory_percent": memory.percent, "available_gb": memory.available / (1024**3)}
            }
        
        return {
            "status": "healthy",
            "message": f"Memory usage normal: {memory.percent:.1f}%",
            "details": {"memory_percent": memory.percent, "available_gb": memory.available / (1024**3)}
        }
    
    async def _check_disk_health(self) -> Dict[str, Any]:
        """Check disk health"""
        disk = psutil.disk_usage('/')
        
        if disk.percent > 95:
            return {
                "status": "unhealthy",
                "message": f"Critical disk usage: {disk.percent:.1f}%",
                "details": {"disk_percent": disk.percent, "free_gb": disk.free / (1024**3)}
            }
        elif disk.percent > 85:
            return {
                "status": "degraded",
                "message": f"High disk usage: {disk.percent:.1f}%",
                "details": {"disk_percent": disk.percent, "free_gb": disk.free / (1024**3)}
            }
        
        return {
            "status": "healthy",
            "message": f"Disk usage normal: {disk.percent:.1f}%",
            "details": {"disk_percent": disk.percent, "free_gb": disk.free / (1024**3)}
        }
    
    async def run_health_check(self, name: str) -> Optional[HealthResult]:
        """Run a specific health check on demand"""
        health_check = self.health_checks.get(name)
        if not health_check:
            return None
        
        return await self._execute_health_check(health_check)
    
    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        if not self.health_results:
            return {
                "status": "unknown",
                "message": "No health checks available",
                "checks": {}
            }
        
        critical_unhealthy = 0
        total_unhealthy = 0
        total_checks = len(self.health_results)
        
        check_details = {}
        
        for name, result in self.health_results.items():
            check_details[name] = {
                "status": result.status.value,
                "message": result.message,
                "response_time": result.response_time,
                "timestamp": result.timestamp.isoformat()
            }
            
            if result.status == HealthStatus.UNHEALTHY:
                total_unhealthy += 1
                health_check = self.health_checks.get(name)
                if health_check and health_check.critical:
                    critical_unhealthy += 1
        
        # Determine overall status
        if critical_unhealthy > 0:
            overall_status = "unhealthy"
            message = f"{critical_unhealthy} critical health checks failing"
        elif total_unhealthy > 0:
            overall_status = "degraded"
            message = f"{total_unhealthy} health checks failing"
        else:
            overall_status = "healthy"
            message = "All health checks passing"
        
        return {
            "status": overall_status,
            "message": message,
            "summary": {
                "total_checks": total_checks,
                "unhealthy_checks": total_unhealthy,
                "critical_unhealthy": critical_unhealthy
            },
            "checks": check_details,
            "system_metrics": self.system_metrics.__dict__ if self.system_metrics else None,
            "timestamp": datetime.now().isoformat()
        }
    
    def get_health_history(self, check_name: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get health check history"""
        history = self.health_history
        
        if check_name:
            history = [h for h in history if h["name"] == check_name]
        
        return sorted(history, key=lambda x: x["timestamp"], reverse=True)[:limit]

# Global health monitor instance
health_monitor = HealthMonitor()
