# app/core/i18n.py
import json
import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
import re

logger = logging.getLogger(__name__)

@dataclass
class LanguageInfo:
    """Information about a supported language"""
    code: str
    name: str
    native_name: str
    rtl: bool = False
    fallback: Optional[str] = None

@dataclass
class TranslationContext:
    """Context for translation"""
    domain: str = "default"
    variables: Dict[str, Any] = field(default_factory=dict)
    plural_count: Optional[int] = None

class I18nManager:
    """Internationalization manager for multi-language support"""
    
    def __init__(self, default_language: str = "en", translations_dir: Optional[str] = None):
        self.default_language = default_language
        self.current_language = default_language
        self.translations_dir = translations_dir or os.path.join(os.path.dirname(__file__), "translations")
        
        # Language configurations
        self.supported_languages: Dict[str, LanguageInfo] = {}
        self.translations: Dict[str, Dict[str, Dict[str, Any]]] = {}
        self.fallback_chain: Dict[str, List[str]] = {}
        
        # Language detection patterns
        self.language_patterns = {
            "en": [r"en[-_]?[A-Z]{0,2}", r"english"],
            "zh": [r"zh[-_]?[A-Z]{0,2}", r"chinese", r"中文"],
            "es": [r"es[-_]?[A-Z]{0,2}", r"spanish", r"español"],
            "fr": [r"fr[-_]?[A-Z]{0,2}", r"french", r"français"],
            "de": [r"de[-_]?[A-Z]{0,2}", r"german", r"deutsch"],
            "ja": [r"ja[-_]?[A-Z]{0,2}", r"japanese", r"日本語"],
            "ko": [r"ko[-_]?[A-Z]{0,2}", r"korean", r"한국어"],
            "pt": [r"pt[-_]?[A-Z]{0,2}", r"portuguese", r"português"],
            "ru": [r"ru[-_]?[A-Z]{0,2}", r"russian", r"русский"],
            "ar": [r"ar[-_]?[A-Z]{0,2}", r"arabic", r"العربية"]
        }
        
        # Initialize default languages and translations
        self._setup_default_languages()
        self._load_translations()
    
    def _setup_default_languages(self):
        """Setup default supported languages"""
        self.supported_languages = {
            "en": LanguageInfo("en", "English", "English", fallback=None),
            "zh": LanguageInfo("zh", "Chinese", "中文", fallback="en"),
            "es": LanguageInfo("es", "Spanish", "Español", fallback="en"),
            "fr": LanguageInfo("fr", "French", "Français", fallback="en"),
            "de": LanguageInfo("de", "German", "Deutsch", fallback="en"),
            "ja": LanguageInfo("ja", "Japanese", "日本語", fallback="en"),
            "ko": LanguageInfo("ko", "Korean", "한국어", fallback="en"),
            "pt": LanguageInfo("pt", "Portuguese", "Português", fallback="en"),
            "ru": LanguageInfo("ru", "Russian", "Русский", fallback="en"),
            "ar": LanguageInfo("ar", "Arabic", "العربية", rtl=True, fallback="en")
        }
        
        # Build fallback chains
        for lang_code, lang_info in self.supported_languages.items():
            chain = [lang_code]
            current = lang_info.fallback
            while current and current not in chain:
                chain.append(current)
                current = self.supported_languages.get(current, {}).fallback
            self.fallback_chain[lang_code] = chain
    
    def _load_translations(self):
        """Load translations from files"""
        translations_path = Path(self.translations_dir)
        
        if not translations_path.exists():
            logger.warning(f"Translations directory not found: {self.translations_dir}")
            self._create_default_translations()
            return
        
        for lang_code in self.supported_languages.keys():
            lang_file = translations_path / f"{lang_code}.json"
            if lang_file.exists():
                try:
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self.translations[lang_code] = json.load(f)
                    logger.info(f"Loaded translations for {lang_code}")
                except Exception as e:
                    logger.error(f"Failed to load translations for {lang_code}: {e}")
            else:
                logger.warning(f"Translation file not found: {lang_file}")
        
        # Ensure we have at least default translations
        if not self.translations:
            self._create_default_translations()
    
    def _create_default_translations(self):
        """Create default translations in memory"""
        self.translations = {
            "en": {
                "common": {
                    "success": "Success",
                    "error": "Error",
                    "warning": "Warning",
                    "loading": "Loading...",
                    "processing": "Processing...",
                    "please_wait": "Please wait",
                    "try_again": "Please try again",
                    "not_found": "Not found",
                    "unauthorized": "Unauthorized",
                    "forbidden": "Forbidden",
                    "timeout": "Request timed out",
                    "rate_limit": "Rate limit exceeded",
                    "internal_error": "Internal server error",
                    "service_unavailable": "Service temporarily unavailable"
                },
                "ai": {
                    "generating_response": "Generating AI response...",
                    "response_ready": "AI response is ready",
                    "response_failed": "Failed to generate AI response",
                    "model_unavailable": "AI model is temporarily unavailable",
                    "context_too_long": "Input context is too long",
                    "invalid_request": "Invalid AI request"
                },
                "travel": {
                    "planning_trip": "Planning your trip...",
                    "trip_ready": "Your travel plan is ready",
                    "destination_not_found": "Destination not found",
                    "invalid_dates": "Invalid travel dates",
                    "no_flights_found": "No flights found for your criteria",
                    "no_hotels_found": "No hotels found for your criteria"
                }
            },
            "zh": {
                "common": {
                    "success": "成功",
                    "error": "错误",
                    "warning": "警告",
                    "loading": "加载中...",
                    "processing": "处理中...",
                    "please_wait": "请稍候",
                    "try_again": "请重试",
                    "not_found": "未找到",
                    "unauthorized": "未授权",
                    "forbidden": "禁止访问",
                    "timeout": "请求超时",
                    "rate_limit": "请求频率超限",
                    "internal_error": "内部服务器错误",
                    "service_unavailable": "服务暂时不可用"
                },
                "ai": {
                    "generating_response": "正在生成AI回复...",
                    "response_ready": "AI回复已准备就绪",
                    "response_failed": "生成AI回复失败",
                    "model_unavailable": "AI模型暂时不可用",
                    "context_too_long": "输入内容过长",
                    "invalid_request": "无效的AI请求"
                },
                "travel": {
                    "planning_trip": "正在规划您的行程...",
                    "trip_ready": "您的旅行计划已准备就绪",
                    "destination_not_found": "未找到目的地",
                    "invalid_dates": "无效的旅行日期",
                    "no_flights_found": "未找到符合条件的航班",
                    "no_hotels_found": "未找到符合条件的酒店"
                }
            },
            "es": {
                "common": {
                    "success": "Éxito",
                    "error": "Error",
                    "warning": "Advertencia",
                    "loading": "Cargando...",
                    "processing": "Procesando...",
                    "please_wait": "Por favor espere",
                    "try_again": "Por favor inténtelo de nuevo",
                    "not_found": "No encontrado",
                    "unauthorized": "No autorizado",
                    "forbidden": "Prohibido",
                    "timeout": "Tiempo de espera agotado",
                    "rate_limit": "Límite de velocidad excedido",
                    "internal_error": "Error interno del servidor",
                    "service_unavailable": "Servicio temporalmente no disponible"
                },
                "ai": {
                    "generating_response": "Generando respuesta de IA...",
                    "response_ready": "La respuesta de IA está lista",
                    "response_failed": "Error al generar respuesta de IA",
                    "model_unavailable": "El modelo de IA no está disponible temporalmente",
                    "context_too_long": "El contexto de entrada es demasiado largo",
                    "invalid_request": "Solicitud de IA inválida"
                },
                "travel": {
                    "planning_trip": "Planificando su viaje...",
                    "trip_ready": "Su plan de viaje está listo",
                    "destination_not_found": "Destino no encontrado",
                    "invalid_dates": "Fechas de viaje inválidas",
                    "no_flights_found": "No se encontraron vuelos para sus criterios",
                    "no_hotels_found": "No se encontraron hoteles para sus criterios"
                }
            }
        }
    
    def detect_language(self, text: str, accept_language: Optional[str] = None) -> str:
        """Detect language from text or Accept-Language header"""
        
        # First try Accept-Language header
        if accept_language:
            detected = self._parse_accept_language(accept_language)
            if detected in self.supported_languages:
                return detected
        
        # Then try to detect from text content
        if text:
            detected = self._detect_from_text(text)
            if detected:
                return detected
        
        return self.default_language
    
    def _parse_accept_language(self, accept_language: str) -> Optional[str]:
        """Parse Accept-Language header"""
        # Simple parsing of Accept-Language header
        # Format: en-US,en;q=0.9,zh;q=0.8
        languages = []
        
        for lang_part in accept_language.split(','):
            lang_part = lang_part.strip()
            if ';' in lang_part:
                lang, quality = lang_part.split(';', 1)
                try:
                    q = float(quality.split('=')[1])
                except:
                    q = 1.0
            else:
                lang = lang_part
                q = 1.0
            
            # Extract base language code
            base_lang = lang.split('-')[0].lower()
            languages.append((base_lang, q))
        
        # Sort by quality score
        languages.sort(key=lambda x: x[1], reverse=True)
        
        # Return first supported language
        for lang, _ in languages:
            if lang in self.supported_languages:
                return lang
        
        return None
    
    def _detect_from_text(self, text: str) -> Optional[str]:
        """Detect language from text content using patterns"""
        text_lower = text.lower()
        
        for lang_code, patterns in self.language_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    return lang_code
        
        return None
    
    def translate(self, 
                 key: str, 
                 language: Optional[str] = None,
                 domain: str = "common",
                 variables: Optional[Dict[str, Any]] = None,
                 plural_count: Optional[int] = None) -> str:
        """Translate a key to the specified language"""
        
        target_language = language or self.current_language
        variables = variables or {}
        
        # Try to find translation in fallback chain
        for lang in self.fallback_chain.get(target_language, [target_language]):
            if lang in self.translations:
                domain_translations = self.translations[lang].get(domain, {})
                if key in domain_translations:
                    translation = domain_translations[key]
                    
                    # Handle pluralization
                    if plural_count is not None and isinstance(translation, dict):
                        if plural_count == 0 and "zero" in translation:
                            translation = translation["zero"]
                        elif plural_count == 1 and "one" in translation:
                            translation = translation["one"]
                        elif "other" in translation:
                            translation = translation["other"]
                        else:
                            translation = str(translation)
                    
                    # Variable substitution
                    if variables and isinstance(translation, str):
                        try:
                            translation = translation.format(**variables)
                        except KeyError as e:
                            logger.warning(f"Missing variable {e} in translation for key '{key}'")
                    
                    return str(translation)
        
        # Return key if no translation found
        logger.warning(f"No translation found for key '{key}' in language '{target_language}'")
        return key
    
    def translate_dict(self, 
                      data: Dict[str, Any], 
                      language: Optional[str] = None,
                      domain: str = "common") -> Dict[str, Any]:
        """Translate all translatable values in a dictionary"""
        
        result = {}
        for key, value in data.items():
            if isinstance(value, str) and value.startswith("i18n:"):
                # Translation key format: "i18n:key_name"
                translation_key = value[5:]  # Remove "i18n:" prefix
                result[key] = self.translate(translation_key, language, domain)
            elif isinstance(value, dict):
                result[key] = self.translate_dict(value, language, domain)
            elif isinstance(value, list):
                result[key] = [
                    self.translate_dict(item, language, domain) if isinstance(item, dict)
                    else self.translate(item[5:], language, domain) if isinstance(item, str) and item.startswith("i18n:")
                    else item
                    for item in value
                ]
            else:
                result[key] = value
        
        return result
    
    def set_language(self, language: str):
        """Set current language"""
        if language in self.supported_languages:
            self.current_language = language
            logger.info(f"Language set to {language}")
        else:
            logger.warning(f"Unsupported language: {language}")
    
    def get_supported_languages(self) -> Dict[str, LanguageInfo]:
        """Get all supported languages"""
        return self.supported_languages.copy()
    
    def add_language(self, language_info: LanguageInfo):
        """Add a new supported language"""
        self.supported_languages[language_info.code] = language_info
        
        # Update fallback chain
        chain = [language_info.code]
        current = language_info.fallback
        while current and current not in chain:
            chain.append(current)
            current = self.supported_languages.get(current, {}).fallback
        self.fallback_chain[language_info.code] = chain
        
        logger.info(f"Added language: {language_info.code}")
    
    def add_translations(self, language: str, domain: str, translations: Dict[str, Any]):
        """Add translations for a language and domain"""
        if language not in self.translations:
            self.translations[language] = {}
        
        if domain not in self.translations[language]:
            self.translations[language][domain] = {}
        
        self.translations[language][domain].update(translations)
        logger.info(f"Added translations for {language}/{domain}")
    
    def save_translations(self, language: str):
        """Save translations to file"""
        if language not in self.translations:
            logger.warning(f"No translations found for language: {language}")
            return
        
        translations_path = Path(self.translations_dir)
        translations_path.mkdir(exist_ok=True)
        
        lang_file = translations_path / f"{language}.json"
        
        try:
            with open(lang_file, 'w', encoding='utf-8') as f:
                json.dump(self.translations[language], f, ensure_ascii=False, indent=2)
            logger.info(f"Saved translations for {language} to {lang_file}")
        except Exception as e:
            logger.error(f"Failed to save translations for {language}: {e}")
    
    def get_language_info(self, language: str) -> Optional[LanguageInfo]:
        """Get information about a language"""
        return self.supported_languages.get(language)
    
    def is_rtl(self, language: Optional[str] = None) -> bool:
        """Check if language is right-to-left"""
        lang = language or self.current_language
        lang_info = self.supported_languages.get(lang)
        return lang_info.rtl if lang_info else False

# Global i18n manager instance
i18n_manager = I18nManager()

# Convenience functions
def t(key: str, language: Optional[str] = None, domain: str = "common", **kwargs) -> str:
    """Shorthand for translate"""
    return i18n_manager.translate(key, language, domain, kwargs)

def detect_language(text: str, accept_language: Optional[str] = None) -> str:
    """Shorthand for language detection"""
    return i18n_manager.detect_language(text, accept_language)

def set_language(language: str):
    """Shorthand for setting language"""
    i18n_manager.set_language(language)
