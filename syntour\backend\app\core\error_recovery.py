# app/core/error_recovery.py
import asyncio
import logging
import time
import traceback
import json
from typing import Dict, Any, Optional, List, Callable, Union, Type
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    DATABASE = "database"
    API_EXTERNAL = "api_external"
    AI_MODEL = "ai_model"
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    TIMEOUT = "timeout"
    RESOURCE = "resource"
    UNKNOWN = "unknown"

class RecoveryAction(Enum):
    """Types of recovery actions"""
    RETRY = "retry"
    FALLBACK = "fallback"
    CIRCUIT_BREAK = "circuit_break"
    CACHE_FALLBACK = "cache_fallback"
    ALTERNATIVE_SERVICE = "alternative_service"
    GRACEFUL_DEGRADATION = "graceful_degradation"
    ALERT_ADMIN = "alert_admin"

@dataclass
class ErrorContext:
    """Context information for an error"""
    error_id: str
    timestamp: datetime
    error_type: str
    error_message: str
    traceback: str
    category: ErrorCategory
    severity: ErrorSeverity
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    endpoint: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration"""
    action: RecoveryAction
    max_attempts: int = 3
    delay: float = 1.0
    backoff_factor: float = 2.0
    timeout: Optional[float] = None
    fallback_func: Optional[Callable] = None
    conditions: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ErrorPattern:
    """Pattern for error classification and recovery"""
    name: str
    error_types: List[Type[Exception]]
    keywords: List[str] = field(default_factory=list)
    category: ErrorCategory = ErrorCategory.UNKNOWN
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
    recovery_strategies: List[RecoveryStrategy] = field(default_factory=list)
    auto_recover: bool = True

class ErrorRecoverySystem:
    """Advanced error recovery system with intelligent classification and recovery"""
    
    def __init__(self):
        self.error_patterns: List[ErrorPattern] = []
        self.error_history: List[ErrorContext] = []
        self.recovery_stats: Dict[str, Dict[str, int]] = {}
        self.max_history_size = 1000
        self.alert_handlers: List[Callable] = []
        
        # Initialize default error patterns
        self._setup_default_patterns()
    
    def _setup_default_patterns(self):
        """Setup default error patterns and recovery strategies"""
        
        # Network errors
        self.add_error_pattern(ErrorPattern(
            name="network_errors",
            error_types=[ConnectionError, TimeoutError, OSError],
            keywords=["connection", "timeout", "network", "unreachable"],
            category=ErrorCategory.NETWORK,
            severity=ErrorSeverity.MEDIUM,
            recovery_strategies=[
                RecoveryStrategy(
                    action=RecoveryAction.RETRY,
                    max_attempts=3,
                    delay=1.0,
                    backoff_factor=2.0
                ),
                RecoveryStrategy(
                    action=RecoveryAction.CACHE_FALLBACK,
                    max_attempts=1
                )
            ]
        ))
        
        # Database errors
        self.add_error_pattern(ErrorPattern(
            name="database_errors",
            error_types=[],  # Will be filled with specific DB exceptions
            keywords=["database", "connection pool", "sql", "query"],
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            recovery_strategies=[
                RecoveryStrategy(
                    action=RecoveryAction.RETRY,
                    max_attempts=2,
                    delay=0.5,
                    backoff_factor=2.0
                ),
                RecoveryStrategy(
                    action=RecoveryAction.CACHE_FALLBACK,
                    max_attempts=1
                ),
                RecoveryStrategy(
                    action=RecoveryAction.ALERT_ADMIN,
                    max_attempts=1
                )
            ]
        ))
        
        # API rate limiting
        self.add_error_pattern(ErrorPattern(
            name="rate_limit_errors",
            error_types=[],
            keywords=["rate limit", "too many requests", "429", "quota"],
            category=ErrorCategory.RATE_LIMIT,
            severity=ErrorSeverity.MEDIUM,
            recovery_strategies=[
                RecoveryStrategy(
                    action=RecoveryAction.RETRY,
                    max_attempts=3,
                    delay=5.0,
                    backoff_factor=2.0
                ),
                RecoveryStrategy(
                    action=RecoveryAction.CACHE_FALLBACK,
                    max_attempts=1
                )
            ]
        ))
        
        # AI model errors
        self.add_error_pattern(ErrorPattern(
            name="ai_model_errors",
            error_types=[],
            keywords=["model", "inference", "generation", "token", "context"],
            category=ErrorCategory.AI_MODEL,
            severity=ErrorSeverity.HIGH,
            recovery_strategies=[
                RecoveryStrategy(
                    action=RecoveryAction.RETRY,
                    max_attempts=2,
                    delay=2.0
                ),
                RecoveryStrategy(
                    action=RecoveryAction.FALLBACK,
                    max_attempts=1
                ),
                RecoveryStrategy(
                    action=RecoveryAction.GRACEFUL_DEGRADATION,
                    max_attempts=1
                )
            ]
        ))
        
        # Authentication errors
        self.add_error_pattern(ErrorPattern(
            name="auth_errors",
            error_types=[],
            keywords=["unauthorized", "authentication", "token", "401", "403"],
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.HIGH,
            recovery_strategies=[
                RecoveryStrategy(
                    action=RecoveryAction.RETRY,
                    max_attempts=1,
                    delay=1.0
                ),
                RecoveryStrategy(
                    action=RecoveryAction.ALERT_ADMIN,
                    max_attempts=1
                )
            ],
            auto_recover=False
        ))
    
    def add_error_pattern(self, pattern: ErrorPattern):
        """Add a new error pattern"""
        self.error_patterns.append(pattern)
        logger.info(f"Added error pattern: {pattern.name}")
    
    def add_alert_handler(self, handler: Callable):
        """Add an alert handler for critical errors"""
        self.alert_handlers.append(handler)
    
    def classify_error(self, error: Exception, context: Dict[str, Any] = None) -> ErrorContext:
        """Classify an error and create error context"""
        error_id = str(uuid.uuid4())
        error_message = str(error)
        error_traceback = traceback.format_exc()
        
        # Find matching pattern
        category = ErrorCategory.UNKNOWN
        severity = ErrorSeverity.MEDIUM
        
        for pattern in self.error_patterns:
            # Check error type
            if any(isinstance(error, error_type) for error_type in pattern.error_types):
                category = pattern.category
                severity = pattern.severity
                break
            
            # Check keywords in error message
            if any(keyword.lower() in error_message.lower() for keyword in pattern.keywords):
                category = pattern.category
                severity = pattern.severity
                break
        
        # Create error context
        error_context = ErrorContext(
            error_id=error_id,
            timestamp=datetime.now(),
            error_type=type(error).__name__,
            error_message=error_message,
            traceback=error_traceback,
            category=category,
            severity=severity,
            request_id=context.get('request_id') if context else None,
            user_id=context.get('user_id') if context else None,
            endpoint=context.get('endpoint') if context else None,
            metadata=context or {}
        )
        
        # Add to history
        self._add_to_history(error_context)
        
        return error_context
    
    def _add_to_history(self, error_context: ErrorContext):
        """Add error to history with size limit"""
        self.error_history.append(error_context)
        
        # Limit history size
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
    
    async def recover_from_error(self, 
                                error: Exception, 
                                original_func: Callable,
                                args: tuple = (),
                                kwargs: Dict[str, Any] = None,
                                context: Dict[str, Any] = None) -> Any:
        """Attempt to recover from an error using configured strategies"""
        kwargs = kwargs or {}
        error_context = self.classify_error(error, context)
        
        logger.error(f"Error occurred: {error_context.error_id} - {error_context.error_message}")
        
        # Find recovery strategies for this error
        recovery_strategies = self._get_recovery_strategies(error_context)
        
        if not recovery_strategies:
            logger.warning(f"No recovery strategies found for error: {error_context.error_id}")
            raise error
        
        # Try each recovery strategy
        for strategy in recovery_strategies:
            try:
                result = await self._execute_recovery_strategy(
                    strategy, error_context, original_func, args, kwargs
                )
                
                # Update recovery stats
                self._update_recovery_stats(error_context.category.value, strategy.action.value, True)
                
                logger.info(f"Successfully recovered from error {error_context.error_id} using {strategy.action.value}")
                return result
                
            except Exception as recovery_error:
                logger.warning(f"Recovery strategy {strategy.action.value} failed: {recovery_error}")
                self._update_recovery_stats(error_context.category.value, strategy.action.value, False)
                continue
        
        # All recovery strategies failed
        logger.error(f"All recovery strategies failed for error: {error_context.error_id}")
        
        # Send alerts for critical errors
        if error_context.severity == ErrorSeverity.CRITICAL:
            await self._send_alerts(error_context)
        
        raise error
    
    def _get_recovery_strategies(self, error_context: ErrorContext) -> List[RecoveryStrategy]:
        """Get recovery strategies for an error"""
        for pattern in self.error_patterns:
            if pattern.category == error_context.category and pattern.auto_recover:
                return pattern.recovery_strategies
        
        return []
    
    async def _execute_recovery_strategy(self,
                                       strategy: RecoveryStrategy,
                                       error_context: ErrorContext,
                                       original_func: Callable,
                                       args: tuple,
                                       kwargs: Dict[str, Any]) -> Any:
        """Execute a specific recovery strategy"""
        
        if strategy.action == RecoveryAction.RETRY:
            return await self._retry_with_backoff(
                original_func, args, kwargs, strategy.max_attempts, 
                strategy.delay, strategy.backoff_factor
            )
        
        elif strategy.action == RecoveryAction.FALLBACK:
            if strategy.fallback_func:
                return await self._execute_fallback(strategy.fallback_func, args, kwargs)
            else:
                raise ValueError("Fallback strategy requires fallback_func")
        
        elif strategy.action == RecoveryAction.CACHE_FALLBACK:
            return await self._cache_fallback(error_context, args, kwargs)
        
        elif strategy.action == RecoveryAction.GRACEFUL_DEGRADATION:
            return await self._graceful_degradation(error_context, args, kwargs)
        
        elif strategy.action == RecoveryAction.ALERT_ADMIN:
            await self._send_alerts(error_context)
            raise Exception("Admin alert sent, manual intervention required")
        
        else:
            raise ValueError(f"Unknown recovery action: {strategy.action}")
    
    async def _retry_with_backoff(self,
                                func: Callable,
                                args: tuple,
                                kwargs: Dict[str, Any],
                                max_attempts: int,
                                delay: float,
                                backoff_factor: float) -> Any:
        """Retry function with exponential backoff"""
        
        for attempt in range(max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            
            except Exception as e:
                if attempt == max_attempts - 1:
                    raise e
                
                wait_time = delay * (backoff_factor ** attempt)
                logger.info(f"Retry attempt {attempt + 1} failed, waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
    
    async def _execute_fallback(self, fallback_func: Callable, args: tuple, kwargs: Dict[str, Any]) -> Any:
        """Execute fallback function"""
        if asyncio.iscoroutinefunction(fallback_func):
            return await fallback_func(*args, **kwargs)
        else:
            return fallback_func(*args, **kwargs)
    
    async def _cache_fallback(self, error_context: ErrorContext, args: tuple, kwargs: Dict[str, Any]) -> Any:
        """Attempt to return cached data as fallback"""
        # This would integrate with the caching system
        # For now, return a placeholder response
        return {
            "success": False,
            "error": "Service temporarily unavailable",
            "fallback": True,
            "cached": True,
            "error_id": error_context.error_id
        }
    
    async def _graceful_degradation(self, error_context: ErrorContext, args: tuple, kwargs: Dict[str, Any]) -> Any:
        """Provide graceful degradation response"""
        return {
            "success": False,
            "error": "Service operating in degraded mode",
            "degraded": True,
            "error_id": error_context.error_id,
            "message": "Some features may be limited due to technical issues"
        }
    
    async def _send_alerts(self, error_context: ErrorContext):
        """Send alerts for critical errors"""
        alert_data = {
            "error_id": error_context.error_id,
            "timestamp": error_context.timestamp.isoformat(),
            "category": error_context.category.value,
            "severity": error_context.severity.value,
            "message": error_context.error_message,
            "endpoint": error_context.endpoint,
            "request_id": error_context.request_id
        }
        
        for handler in self.alert_handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(alert_data)
                else:
                    handler(alert_data)
            except Exception as e:
                logger.error(f"Alert handler failed: {e}")
    
    def _update_recovery_stats(self, category: str, action: str, success: bool):
        """Update recovery statistics"""
        if category not in self.recovery_stats:
            self.recovery_stats[category] = {}
        
        if action not in self.recovery_stats[category]:
            self.recovery_stats[category][action] = {"success": 0, "failure": 0}
        
        if success:
            self.recovery_stats[category][action]["success"] += 1
        else:
            self.recovery_stats[category][action]["failure"] += 1
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error and recovery statistics"""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_day = now - timedelta(days=1)
        
        recent_errors = [e for e in self.error_history if e.timestamp >= last_hour]
        daily_errors = [e for e in self.error_history if e.timestamp >= last_day]
        
        stats = {
            "total_errors": len(self.error_history),
            "errors_last_hour": len(recent_errors),
            "errors_last_day": len(daily_errors),
            "error_categories": {},
            "error_severity": {},
            "recovery_stats": self.recovery_stats
        }
        
        # Count by category and severity
        for error in self.error_history:
            category = error.category.value
            severity = error.severity.value
            
            stats["error_categories"][category] = stats["error_categories"].get(category, 0) + 1
            stats["error_severity"][severity] = stats["error_severity"].get(severity, 0) + 1
        
        return stats
    
    def get_recent_errors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent errors for monitoring"""
        recent_errors = sorted(self.error_history, key=lambda x: x.timestamp, reverse=True)[:limit]
        
        return [
            {
                "error_id": error.error_id,
                "timestamp": error.timestamp.isoformat(),
                "type": error.error_type,
                "message": error.error_message,
                "category": error.category.value,
                "severity": error.severity.value,
                "endpoint": error.endpoint,
                "request_id": error.request_id
            }
            for error in recent_errors
        ]

# Global error recovery system instance
error_recovery_system = ErrorRecoverySystem()
