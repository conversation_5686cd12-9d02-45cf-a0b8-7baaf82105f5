                                                            #!/usr/bin/env python3
"""
SynTour 增强后端快速集成设置脚本
Quick Integration Setup Script for SynTour Enhanced Backend

使用方法:
python scripts/quick_integration_setup.py --mode=setup
python scripts/quick_integration_setup.py --mode=verify
python scripts/quick_integration_setup.py --mode=rollback
"""

import os
import sys
import argparse
import subprocess
import shutil
import json
from datetime import datetime
from pathlib import Path

class QuickIntegrationSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "backend"
        self.backup_dir = self.project_root / "backups"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def print_step(self, step_num, description):
        """打印步骤信息"""
        print(f"\n{'='*60}")
        print(f"步骤 {step_num}: {description}")
        print('='*60)
    
    def run_command(self, command, cwd=None, check=True):
        """运行命令并处理错误"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd or self.backend_dir,
                capture_output=True, 
                text=True,
                check=check
            )
            if result.stdout:
                print(result.stdout)
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ 命令执行失败: {command}")
            print(f"错误: {e.stderr}")
            if check:
                sys.exit(1)
            return e
    
    def create_backup(self):
        """创建备份"""
        self.print_step(1, "创建系统备份")
        
        # 创建备份目录
        backup_path = self.backup_dir / f"backup_{self.timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份代码
        if self.backend_dir.exists():
            shutil.copytree(
                self.backend_dir, 
                backup_path / "backend",
                ignore=shutil.ignore_patterns('__pycache__', '*.pyc', 'gotrip_env')
            )
            print(f"✅ 代码备份完成: {backup_path / 'backend'}")
        
        # 备份环境文件
        env_file = self.project_root / ".env"
        if env_file.exists():
            shutil.copy2(env_file, backup_path / ".env")
            print(f"✅ 环境文件备份完成: {backup_path / '.env'}")
        
        # 备份数据库（如果配置了）
        self.backup_database(backup_path)
        
        return backup_path
    
    def backup_database(self, backup_path):
        """备份数据库"""
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            try:
                backup_file = backup_path / f"database_backup_{self.timestamp}.sql"
                # 简化的数据库备份命令
                cmd = f"pg_dump {database_url} > {backup_file}"
                result = self.run_command(cmd, check=False)
                if result.returncode == 0:
                    print(f"✅ 数据库备份完成: {backup_file}")
                else:
                    print("⚠️ 数据库备份失败，请手动备份")
            except Exception as e:
                print(f"⚠️ 数据库备份失败: {e}")
    
    def install_dependencies(self):
        """安装新依赖项"""
        self.print_step(2, "安装新依赖项")
        
        new_dependencies = [
            "asyncpg==0.29.0",
            "redis==5.0.1", 
            "circuitbreaker==1.4.0",
            "asyncio-throttle==1.0.2",
            "tenacity==8.2.3",
            "psutil==5.9.6"
        ]
        
        # 检查虚拟环境
        venv_path = self.backend_dir / "gotrip_env"
        if not venv_path.exists():
            print("❌ 虚拟环境不存在，请先创建虚拟环境")
            sys.exit(1)
        
        # 激活虚拟环境并安装依赖
        if os.name == 'nt':  # Windows
            pip_cmd = str(venv_path / "Scripts" / "pip")
        else:  # Linux/Mac
            pip_cmd = str(venv_path / "bin" / "pip")
        
        for dep in new_dependencies:
            print(f"安装 {dep}...")
            self.run_command(f"{pip_cmd} install {dep}")
        
        # 更新 requirements.txt
        self.run_command(f"{pip_cmd} freeze > requirements.txt")
        print("✅ 依赖项安装完成")
    
    def setup_environment(self):
        """设置环境变量"""
        self.print_step(3, "配置环境变量")
        
        env_file = self.project_root / ".env"
        
        # 读取现有环境变量
        existing_env = {}
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        existing_env[key] = value
        
        # 新的环境变量
        new_env_vars = {
            "# Enhanced Backend Configuration": "",
            "MAX_CONCURRENT_TASKS": "10",
            "HEALTH_CHECK_INTERVAL": "30.0", 
            "DEFAULT_LANGUAGE": "zh",
            "DB_POOL_MIN_SIZE": "5",
            "DB_POOL_MAX_SIZE": "20",
            "HTTP_POOL_MAX_SIZE": "20",
            "CIRCUIT_BREAKER_FAILURE_THRESHOLD": "5",
            "CIRCUIT_BREAKER_RECOVERY_TIMEOUT": "60",
            "CACHE_TTL_SECONDS": "300",
            "LOG_LEVEL": "INFO",
            "METRICS_ENABLED": "true"
        }
        
        # 合并环境变量
        for key, value in new_env_vars.items():
            if key not in existing_env:
                existing_env[key] = value
        
        # 写入环境文件
        with open(env_file, 'w', encoding='utf-8') as f:
            for key, value in existing_env.items():
                if key.startswith('#'):
                    f.write(f"\n{key}\n")
                else:
                    f.write(f"{key}={value}\n")
        
        print(f"✅ 环境变量配置完成: {env_file}")
        
        # 提示用户配置数据库
        if "DATABASE_URL" not in existing_env:
            print("\n⚠️ 请手动配置以下环境变量:")
            print("DATABASE_URL=postgresql://username:password@localhost:5432/syntour_db")
            print("REDIS_URL=redis://localhost:6379/0  # 可选")
    
    def modify_main_app(self):
        """修改主应用程序文件"""
        self.print_step(4, "修改主应用程序文件")
        
        main_py = self.backend_dir / "main.py"
        if not main_py.exists():
            print("❌ main.py 文件不存在")
            sys.exit(1)
        
        # 读取现有文件
        with open(main_py, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经集成
        if "enhanced_backend_manager" in content:
            print("✅ main.py 已经包含增强后端管理器")
            return
        
        # 备份原文件
        backup_main = main_py.with_suffix('.py.backup')
        shutil.copy2(main_py, backup_main)
        print(f"✅ main.py 备份到: {backup_main}")
        
        # 添加导入语句
        import_lines = [
            "from app.core.enhanced_backend_manager import enhanced_backend_manager",
            "from app.core.response_formatter import response_formatter", 
            "from app.routers.enhanced_ai_router import router as enhanced_ai_router",
            "from contextlib import asynccontextmanager"
        ]
        
        # 在现有导入后添加新导入
        lines = content.split('\n')
        import_index = -1
        for i, line in enumerate(lines):
            if line.startswith('from ') or line.startswith('import '):
                import_index = i
        
        if import_index >= 0:
            for import_line in reversed(import_lines):
                if import_line not in content:
                    lines.insert(import_index + 1, import_line)
        
        # 添加生命周期管理
        lifespan_code = '''
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    try:
        await enhanced_backend_manager.initialize()
        logger.info("增强后端系统初始化成功")
        yield
    except Exception as e:
        logger.error(f"增强后端系统初始化失败: {e}")
        raise
    finally:
        await enhanced_backend_manager.shutdown()
        logger.info("增强后端系统已关闭")
'''
        
        # 查找 FastAPI 应用创建位置
        app_creation_index = -1
        for i, line in enumerate(lines):
            if 'app = FastAPI(' in line:
                app_creation_index = i
                break
        
        if app_creation_index >= 0:
            # 在 FastAPI 创建之前添加生命周期函数
            lines.insert(app_creation_index, lifespan_code)
            
            # 修改 FastAPI 创建行以包含 lifespan
            app_line = lines[app_creation_index + len(lifespan_code.split('\n'))]
            if 'lifespan=' not in app_line:
                app_line = app_line.replace(')', ', lifespan=lifespan)')
                lines[app_creation_index + len(lifespan_code.split('\n'))] = app_line
        
        # 添加增强路由
        router_code = '''
# 增强后端路由
app.include_router(enhanced_ai_router)

@app.get("/api/v2/health")
async def enhanced_health_check():
    """增强健康检查端点"""
    system_status = enhanced_backend_manager.get_system_status()
    return response_formatter.format_health_check(
        status=system_status.get("health_summary", {}).get("status", "unknown"),
        checks=system_status.get("health_summary", {}).get("checks", {}),
        system_metrics=system_status.get("health_summary", {}).get("system_metrics")
    ).dict()
'''
        
        # 在文件末尾添加路由代码
        lines.append(router_code)
        
        # 写入修改后的文件
        with open(main_py, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ main.py 修改完成")
    
    def create_database_tables(self):
        """创建数据库表"""
        self.print_step(5, "创建数据库表")
        
        database_url = os.getenv("DATABASE_URL")
        if not database_url:
            print("⚠️ 未配置 DATABASE_URL，跳过数据库表创建")
            return
        
        sql_commands = [
            """
            CREATE TABLE IF NOT EXISTS system_health_logs (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                component VARCHAR(100),
                status VARCHAR(20),
                details JSONB
            );
            """,
            """
            CREATE TABLE IF NOT EXISTS error_logs (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                error_id VARCHAR(100),
                category VARCHAR(50),
                severity VARCHAR(20),
                message TEXT,
                context JSONB
            );
            """
        ]
        
        try:
            for sql in sql_commands:
                cmd = f'psql "{database_url}" -c "{sql.strip()}"'
                result = self.run_command(cmd, check=False)
                if result.returncode != 0:
                    print(f"⚠️ SQL 执行失败: {sql[:50]}...")
            
            print("✅ 数据库表创建完成")
        except Exception as e:
            print(f"⚠️ 数据库表创建失败: {e}")
    
    def run_verification(self):
        """运行验证测试"""
        self.print_step(6, "运行集成验证")
        
        verify_script = self.project_root / "scripts" / "verify_integration.py"
        if verify_script.exists():
            print("运行集成验证脚本...")
            result = self.run_command(f"python {verify_script}", check=False)
            if result.returncode == 0:
                print("✅ 集成验证通过")
                return True
            else:
                print("❌ 集成验证失败")
                return False
        else:
            print("⚠️ 验证脚本不存在，请手动验证")
            return True
    
    def setup_mode(self):
        """设置模式 - 完整集成流程"""
        print("🚀 开始 SynTour 增强后端快速集成设置")
        
        try:
            backup_path = self.create_backup()
            self.install_dependencies()
            self.setup_environment()
            self.modify_main_app()
            self.create_database_tables()
            
            print(f"\n🎉 集成设置完成！")
            print(f"📁 备份位置: {backup_path}")
            print("\n下一步:")
            print("1. 检查并更新 .env 文件中的数据库连接")
            print("2. 启动应用: uvicorn main:app --reload")
            print("3. 运行验证: python scripts/quick_integration_setup.py --mode=verify")
            
        except Exception as e:
            print(f"\n💥 集成设置失败: {e}")
            print("请检查错误信息并手动修复")
            sys.exit(1)
    
    def verify_mode(self):
        """验证模式 - 运行集成验证"""
        print("🔍 运行集成验证")
        
        success = self.run_verification()
        if success:
            print("\n🎉 验证成功！增强后端集成完成！")
        else:
            print("\n⚠️ 验证失败，请检查配置")
            sys.exit(1)
    
    def rollback_mode(self):
        """回滚模式 - 恢复到集成前状态"""
        print("🔄 开始回滚到集成前状态")
        
        # 查找最新备份
        if not self.backup_dir.exists():
            print("❌ 没有找到备份目录")
            sys.exit(1)
        
        backups = sorted([d for d in self.backup_dir.iterdir() if d.is_dir() and d.name.startswith("backup_")])
        if not backups:
            print("❌ 没有找到备份")
            sys.exit(1)
        
        latest_backup = backups[-1]
        print(f"使用备份: {latest_backup}")
        
        # 恢复代码
        if (latest_backup / "backend").exists():
            if self.backend_dir.exists():
                shutil.rmtree(self.backend_dir)
            shutil.copytree(latest_backup / "backend", self.backend_dir)
            print("✅ 代码已恢复")
        
        # 恢复环境文件
        if (latest_backup / ".env").exists():
            shutil.copy2(latest_backup / ".env", self.project_root / ".env")
            print("✅ 环境文件已恢复")
        
        print("🎉 回滚完成！")

def main():
    parser = argparse.ArgumentParser(description="SynTour 增强后端快速集成工具")
    parser.add_argument(
        "--mode", 
        choices=["setup", "verify", "rollback"],
        required=True,
        help="运行模式: setup(设置), verify(验证), rollback(回滚)"
    )
    
    args = parser.parse_args()
    
    setup = QuickIntegrationSetup()
    
    if args.mode == "setup":
        setup.setup_mode()
    elif args.mode == "verify":
        setup.verify_mode()
    elif args.mode == "rollback":
        setup.rollback_mode()

if __name__ == "__main__":
    main()
