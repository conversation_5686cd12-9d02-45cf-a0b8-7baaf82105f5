# app/routers/enhanced_ai_router.py
from fastapi import APIRouter, Request, HTTPException, Depends, Header
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import time
import logging

# Import enhanced backend systems
from app.core.enhanced_backend_manager import enhanced_backend_manager
from app.core.response_formatter import response_formatter, ResponseStatus, ErrorCode
from app.core.content_length_controller import DeviceType, ContentType, responsive_formatter
from app.core.async_task_manager import TaskConfig, TaskPriority
from app.core.error_recovery import error_recovery_system
from app.core.i18n import detect_language, t
from app.models.chat_models import ChatRequest, ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v2/ai", tags=["Enhanced AI"])

async def get_device_info(request: Request, user_agent: Optional[str] = Header(None)) -> Dict[str, Any]:
    """Extract device information from request"""
    user_agent = user_agent or request.headers.get("User-Agent", "")
    
    # Try to get screen width from custom header
    screen_width = request.headers.get("X-Screen-Width")
    if screen_width:
        try:
            screen_width = int(screen_width)
        except ValueError:
            screen_width = None
    
    device_type = responsive_formatter.content_controller.detect_device_type(user_agent, screen_width)
    
    return {
        "device_type": device_type,
        "user_agent": user_agent,
        "screen_width": screen_width
    }

async def detect_user_language(request: Request, message: str) -> str:
    """Detect user's preferred language"""
    accept_language = request.headers.get("Accept-Language")
    return detect_language(message, accept_language)

@router.post("/chat/enhanced", response_model=Dict[str, Any])
async def enhanced_ai_chat(
    request: ChatRequest,
    http_request: Request,
    device_info: Dict[str, Any] = Depends(get_device_info)
):
    """
    Enhanced AI chat endpoint with all optimizations:
    - Connection pooling and circuit breakers
    - Intelligent content length control
    - Response formatting and i18n
    - Async task management
    - Error recovery
    """
    start_time = time.time()
    request_id = response_formatter.metadata.request_id
    
    try:
        # Detect user language
        user_language = await detect_user_language(http_request, request.message)
        
        # Submit AI processing as async task
        task_id = await enhanced_backend_manager.task_manager.submit_task(
            process_ai_request,
            args=(request.message, user_language, device_info),
            config=TaskConfig(
                priority=TaskPriority.HIGH,
                max_retries=2,
                timeout=30.0,
                metadata={
                    "request_id": request_id,
                    "user_language": user_language,
                    "device_type": device_info["device_type"].value
                }
            )
        )
        
        # Wait for task completion
        task_result = await enhanced_backend_manager.task_manager.wait_for_task(
            task_id, timeout=35.0
        )
        
        if task_result.status.value == "completed":
            ai_response = task_result.result
            
            # Apply content length control
            formatted_response = responsive_formatter.format_for_device(
                content=ai_response["content"],
                device_type=device_info["device_type"],
                content_type=ContentType.AI_RESPONSE,
                user_agent=device_info["user_agent"],
                screen_width=device_info["screen_width"]
            )
            
            # Format response with i18n
            response = response_formatter.format_ai_response(
                content=formatted_response["formatted_content"],
                model_name=ai_response.get("model_name", "gemini-pro"),
                confidence=ai_response.get("confidence"),
                is_truncated=formatted_response["is_truncated"],
                continuation_token=formatted_response.get("continuation_token"),
                processing_time=(time.time() - start_time) * 1000,
                request_id=request_id
            )
            
            # Add device-specific metadata
            response.metadata.source = f"{response.metadata.source}+enhanced"
            response.data = {
                "response": formatted_response["formatted_content"],
                "device_optimizations": formatted_response.get("device_optimizations", []),
                "content_metrics": formatted_response.get("metrics", {}),
                "language": user_language
            }
            
            return response.dict()
            
        else:
            # Task failed
            error_msg = task_result.error or "AI processing failed"
            raise HTTPException(status_code=500, detail=error_msg)
    
    except Exception as e:
        # Use error recovery system
        try:
            recovery_result = await error_recovery_system.recover_from_error(
                e, process_ai_request, 
                args=(request.message, user_language, device_info),
                context={
                    "request_id": request_id,
                    "endpoint": "/api/v2/ai/chat/enhanced",
                    "user_language": user_language
                }
            )
            
            if recovery_result:
                # Format recovered response
                response = response_formatter.format_ai_response(
                    content=recovery_result.get("content", "Service temporarily degraded"),
                    model_name="fallback",
                    processing_time=(time.time() - start_time) * 1000,
                    request_id=request_id
                )
                response.warnings = ["Response generated using fallback mechanism"]
                return response.dict()
        
        except Exception:
            pass  # Fall through to error response
        
        # Format error response
        error_response = response_formatter.format_error(
            ErrorCode.AI_MODEL_ERROR,
            t("ai.response_failed", user_language),
            details={"original_error": str(e)},
            request_id=request_id,
            language=user_language
        )
        
        return JSONResponse(
            status_code=500,
            content=error_response.dict()
        )

async def process_ai_request(message: str, language: str, device_info: Dict[str, Any]) -> Dict[str, Any]:
    """Process AI request with enhanced backend systems"""
    
    # This would integrate with your actual AI model
    # For demonstration, we'll simulate AI processing
    
    # Simulate processing time
    import asyncio
    await asyncio.sleep(0.5)
    
    # Generate response based on device type
    device_type = device_info["device_type"]
    
    if device_type == DeviceType.SMART_WATCH:
        content = "Quick travel tip: Book flights on Tuesday for best deals!"
    elif device_type == DeviceType.MOBILE:
        content = """Here's your travel recommendation:

• Book flights on Tuesday-Wednesday for 20% savings
• Consider shoulder season travel (Apr-May, Sep-Oct)
• Use travel apps for last-minute hotel deals

Need more details? Ask me anything!"""
    else:
        content = """Here's a comprehensive travel recommendation for you:

**Flight Booking Tips:**
• Tuesday and Wednesday are typically the cheapest days to book flights
• Book domestic flights 1-3 months in advance, international 2-8 months
• Use incognito mode to avoid price tracking cookies

**Accommodation Strategies:**
• Consider shoulder season travel (April-May, September-October) for 30-50% savings
• Use hotel comparison sites and check direct booking for price matching
• Look into vacation rentals for longer stays

**Money-Saving Tips:**
• Travel with carry-on only to avoid baggage fees
• Use travel reward credit cards for points and perks
• Download offline maps and translation apps before traveling

**Safety and Planning:**
• Check visa requirements and passport expiration dates
• Research local customs and tipping practices
• Consider travel insurance for international trips

Would you like me to help you plan a specific trip or provide more detailed information about any of these topics?"""
    
    return {
        "content": content,
        "model_name": "gemini-pro-enhanced",
        "confidence": 0.95,
        "language": language,
        "device_optimized": True
    }

@router.get("/chat/continue/{continuation_token}")
async def continue_ai_response(
    continuation_token: str,
    device_info: Dict[str, Any] = Depends(get_device_info)
):
    """Continue a truncated AI response"""
    
    # Get full content using continuation token
    full_content = responsive_formatter.content_controller.continue_content(continuation_token)
    
    if not full_content:
        error_response = response_formatter.format_error(
            ErrorCode.NOT_FOUND,
            "Continuation token not found or expired"
        )
        return JSONResponse(status_code=404, content=error_response.dict())
    
    # Format full content for device
    formatted_response = responsive_formatter.format_for_device(
        content=full_content,
        device_type=device_info["device_type"],
        content_type=ContentType.AI_RESPONSE,
        user_agent=device_info["user_agent"],
        screen_width=device_info["screen_width"]
    )
    
    response = response_formatter.format_ai_response(
        content=formatted_response["formatted_content"],
        model_name="gemini-pro-enhanced",
        is_truncated=formatted_response["is_truncated"],
        continuation_token=formatted_response.get("continuation_token")
    )
    
    return response.dict()

@router.get("/health")
async def ai_system_health():
    """Get AI system health status"""
    
    system_status = enhanced_backend_manager.get_system_status()
    
    health_response = response_formatter.format_health_check(
        status=system_status.get("health_summary", {}).get("status", "unknown"),
        checks=system_status.get("health_summary", {}).get("checks", {}),
        system_metrics=system_status.get("health_summary", {}).get("system_metrics")
    )
    
    return health_response.dict()

@router.get("/metrics")
async def ai_system_metrics():
    """Get detailed system metrics"""
    
    metrics = {
        "task_manager": enhanced_backend_manager.task_manager.get_stats(),
        "circuit_breakers": enhanced_backend_manager.circuit_manager.get_health_summary(),
        "connection_pools": enhanced_backend_manager.connection_manager.get_health_status(),
        "error_recovery": enhanced_backend_manager.error_recovery.get_error_statistics()
    }
    
    # Add API client metrics
    api_metrics = {}
    for name, client in enhanced_backend_manager.api_clients.items():
        api_metrics[name] = client.get_health_status()
    
    metrics["api_clients"] = api_metrics
    
    response = response_formatter.format_api_data(
        data=metrics,
        api_source="system_metrics"
    )
    
    return response.dict()

@router.post("/test/device-optimization")
async def test_device_optimization(
    content: str,
    device_info: Dict[str, Any] = Depends(get_device_info)
):
    """Test content optimization for different devices"""
    
    # Test content formatting for the detected device
    formatted_response = responsive_formatter.format_for_device(
        content=content,
        device_type=device_info["device_type"],
        content_type=ContentType.AI_RESPONSE,
        user_agent=device_info["user_agent"],
        screen_width=device_info["screen_width"]
    )
    
    response = response_formatter.format_api_data(
        data={
            "original_content": content,
            "formatted_content": formatted_response["formatted_content"],
            "device_type": formatted_response["device_type"],
            "is_truncated": formatted_response["is_truncated"],
            "optimizations": formatted_response.get("device_optimizations", []),
            "metrics": formatted_response.get("metrics", {}),
            "continuation_token": formatted_response.get("continuation_token")
        },
        api_source="device_optimizer"
    )
    
    return response.dict()
