# app/core/speech_recognition_optimizer.py
"""
语音识别优化器
Speech Recognition Optimizer

提供高级的语音识别功能，包括：
- 智能音频缓冲区管理
- 音频质量检测和优化
- 多语言检测
- 噪音抑制
- 实时性能监控
"""

import asyncio
import logging
import time
import json
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import io
import wave

logger = logging.getLogger(__name__)

class AudioQuality(Enum):
    """音频质量等级"""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"

class RecognitionMode(Enum):
    """识别模式"""
    REAL_TIME = "real_time"
    BATCH = "batch"
    STREAMING = "streaming"

@dataclass
class AudioMetrics:
    """音频指标"""
    sample_rate: int = 16000
    channels: int = 1
    bit_depth: int = 16
    duration_seconds: float = 0.0
    volume_level: float = 0.0
    noise_level: float = 0.0
    quality: AudioQuality = AudioQuality.FAIR
    is_speech_detected: bool = False

@dataclass
class RecognitionStats:
    """识别统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_processing_time: float = 0.0
    avg_confidence: float = 0.0
    languages_detected: Dict[str, int] = field(default_factory=dict)
    last_recognition_time: Optional[float] = None

class AdvancedAudioBuffer:
    """高级音频缓冲区管理器"""
    
    def __init__(self, 
                 max_size: int = 1024 * 1024,
                 sample_rate: int = 16000,
                 channels: int = 1):
        self.max_size = max_size
        self.sample_rate = sample_rate
        self.channels = channels
        self.buffer = bytearray()
        self.chunks = []  # 存储音频块的时间戳
        self.total_received = 0
        self.last_activity_time = time.time()
        
    def add_chunk(self, data: bytes, timestamp: Optional[float] = None) -> bool:
        """添加音频块"""
        if not data:
            return False
            
        current_time = timestamp or time.time()
        
        # 检查缓冲区大小
        if len(self.buffer) + len(data) > self.max_size:
            self._trim_buffer()
        
        self.buffer.extend(data)
        self.chunks.append({
            'size': len(data),
            'timestamp': current_time,
            'offset': len(self.buffer) - len(data)
        })
        
        self.total_received += len(data)
        self.last_activity_time = current_time
        
        return True
    
    def _trim_buffer(self):
        """修剪缓冲区，保留最新的数据"""
        target_size = self.max_size // 2
        bytes_to_remove = len(self.buffer) - target_size
        
        if bytes_to_remove > 0:
            # 移除旧数据
            self.buffer = self.buffer[bytes_to_remove:]
            
            # 更新块信息
            self.chunks = [
                chunk for chunk in self.chunks 
                if chunk['offset'] >= bytes_to_remove
            ]
            
            # 调整偏移量
            for chunk in self.chunks:
                chunk['offset'] -= bytes_to_remove
                
            logger.debug(f"Trimmed buffer: removed {bytes_to_remove} bytes")
    
    def get_audio_data(self, max_duration: Optional[float] = None) -> bytes:
        """获取音频数据"""
        if max_duration is None:
            return bytes(self.buffer)
        
        # 计算需要的字节数
        bytes_per_second = self.sample_rate * self.channels * 2  # 16-bit
        max_bytes = int(max_duration * bytes_per_second)
        
        if len(self.buffer) <= max_bytes:
            return bytes(self.buffer)
        
        # 返回最新的数据
        return bytes(self.buffer[-max_bytes:])
    
    def get_metrics(self) -> AudioMetrics:
        """获取音频指标"""
        if not self.buffer:
            return AudioMetrics()
        
        # 计算基本指标
        duration = len(self.buffer) / (self.sample_rate * self.channels * 2)
        
        # 简单的音量检测
        try:
            audio_array = np.frombuffer(self.buffer, dtype=np.int16)
            volume_level = float(np.sqrt(np.mean(audio_array**2)))
            noise_level = float(np.std(audio_array))
            
            # 简单的语音检测
            is_speech = volume_level > 1000 and noise_level > 500
            
            # 质量评估
            if volume_level > 5000 and noise_level < 2000:
                quality = AudioQuality.EXCELLENT
            elif volume_level > 3000 and noise_level < 3000:
                quality = AudioQuality.GOOD
            elif volume_level > 1000:
                quality = AudioQuality.FAIR
            else:
                quality = AudioQuality.POOR
                
        except Exception as e:
            logger.warning(f"Audio analysis failed: {e}")
            volume_level = 0.0
            noise_level = 0.0
            is_speech = False
            quality = AudioQuality.POOR
        
        return AudioMetrics(
            sample_rate=self.sample_rate,
            channels=self.channels,
            duration_seconds=duration,
            volume_level=volume_level,
            noise_level=noise_level,
            quality=quality,
            is_speech_detected=is_speech
        )
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
        self.chunks.clear()
    
    def is_silent(self, threshold_seconds: float = 2.0) -> bool:
        """检查是否静音"""
        return time.time() - self.last_activity_time > threshold_seconds

class SpeechRecognitionOptimizer:
    """语音识别优化器"""
    
    def __init__(self):
        self.stats = RecognitionStats()
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.language_detector = LanguageDetector()
        
    def create_session(self, 
                      client_id: str, 
                      mode: RecognitionMode = RecognitionMode.REAL_TIME,
                      config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建识别会话"""
        session_config = config or {}
        
        session = {
            'client_id': client_id,
            'mode': mode,
            'buffer': AdvancedAudioBuffer(
                max_size=session_config.get('max_buffer_size', 1024 * 1024),
                sample_rate=session_config.get('sample_rate', 16000)
            ),
            'created_at': time.time(),
            'last_activity': time.time(),
            'recognition_count': 0,
            'config': session_config,
            'language_hints': session_config.get('language_hints', ['en-US', 'zh-CN']),
            'quality_threshold': session_config.get('quality_threshold', AudioQuality.FAIR)
        }
        
        self.active_sessions[client_id] = session
        logger.info(f"Created speech recognition session for {client_id}")
        
        return session
    
    def add_audio_data(self, client_id: str, data: bytes) -> bool:
        """添加音频数据"""
        if client_id not in self.active_sessions:
            logger.warning(f"No active session for client {client_id}")
            return False
        
        session = self.active_sessions[client_id]
        session['buffer'].add_chunk(data)
        session['last_activity'] = time.time()
        
        return True
    
    def should_process_recognition(self, 
                                 client_id: str,
                                 min_duration: float = 1.0,
                                 max_duration: float = 10.0) -> Tuple[bool, str]:
        """检查是否应该进行识别"""
        if client_id not in self.active_sessions:
            return False, "No active session"
        
        session = self.active_sessions[client_id]
        buffer = session['buffer']
        metrics = buffer.get_metrics()
        
        # 检查音频质量
        quality_threshold = session['quality_threshold']
        if metrics.quality.value < quality_threshold.value:
            return False, f"Audio quality too low: {metrics.quality.value}"
        
        # 检查持续时间
        if metrics.duration_seconds < min_duration:
            return False, f"Audio too short: {metrics.duration_seconds:.2f}s"
        
        if metrics.duration_seconds > max_duration:
            return True, "Max duration reached"
        
        # 检查是否检测到语音
        if not metrics.is_speech_detected:
            return False, "No speech detected"
        
        return True, "Ready for recognition"
    
    def get_optimized_audio(self, 
                          client_id: str,
                          max_duration: Optional[float] = None) -> Optional[bytes]:
        """获取优化的音频数据"""
        if client_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[client_id]
        buffer = session['buffer']
        
        # 获取音频数据
        audio_data = buffer.get_audio_data(max_duration)
        
        if not audio_data:
            return None
        
        # 应用音频优化
        try:
            optimized_audio = self._apply_audio_optimization(audio_data)
            return optimized_audio
        except Exception as e:
            logger.error(f"Audio optimization failed: {e}")
            return audio_data
    
    def _apply_audio_optimization(self, audio_data: bytes) -> bytes:
        """应用音频优化"""
        try:
            # 转换为numpy数组
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # 简单的噪音抑制
            # 移除低于阈值的信号
            threshold = np.max(np.abs(audio_array)) * 0.1
            audio_array = np.where(np.abs(audio_array) < threshold, 0, audio_array)
            
            # 音量标准化
            max_val = np.max(np.abs(audio_array))
            if max_val > 0:
                audio_array = audio_array * (16384 / max_val)  # 标准化到合理音量
            
            return audio_array.astype(np.int16).tobytes()
            
        except Exception as e:
            logger.error(f"Audio optimization error: {e}")
            return audio_data
    
    def update_recognition_stats(self, 
                               client_id: str,
                               success: bool,
                               processing_time: float,
                               confidence: Optional[float] = None,
                               detected_language: Optional[str] = None):
        """更新识别统计信息"""
        self.stats.total_requests += 1
        
        if success:
            self.stats.successful_requests += 1
            
            # 更新平均处理时间
            total_time = self.stats.avg_processing_time * (self.stats.successful_requests - 1)
            self.stats.avg_processing_time = (total_time + processing_time) / self.stats.successful_requests
            
            # 更新平均置信度
            if confidence is not None:
                total_conf = self.stats.avg_confidence * (self.stats.successful_requests - 1)
                self.stats.avg_confidence = (total_conf + confidence) / self.stats.successful_requests
            
            # 更新语言统计
            if detected_language:
                self.stats.languages_detected[detected_language] = (
                    self.stats.languages_detected.get(detected_language, 0) + 1
                )
        else:
            self.stats.failed_requests += 1
        
        self.stats.last_recognition_time = time.time()
        
        # 更新会话统计
        if client_id in self.active_sessions:
            self.active_sessions[client_id]['recognition_count'] += 1
    
    def get_session_metrics(self, client_id: str) -> Optional[Dict[str, Any]]:
        """获取会话指标"""
        if client_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[client_id]
        audio_metrics = session['buffer'].get_metrics()
        
        return {
            'client_id': client_id,
            'mode': session['mode'].value,
            'created_at': session['created_at'],
            'last_activity': session['last_activity'],
            'recognition_count': session['recognition_count'],
            'audio_metrics': {
                'duration_seconds': audio_metrics.duration_seconds,
                'volume_level': audio_metrics.volume_level,
                'noise_level': audio_metrics.noise_level,
                'quality': audio_metrics.quality.value,
                'is_speech_detected': audio_metrics.is_speech_detected
            },
            'buffer_size': len(session['buffer'].buffer),
            'total_received': session['buffer'].total_received
        }
    
    def cleanup_session(self, client_id: str):
        """清理会话"""
        if client_id in self.active_sessions:
            session = self.active_sessions[client_id]
            session['buffer'].clear()
            del self.active_sessions[client_id]
            logger.info(f"Cleaned up session for {client_id}")
    
    def cleanup_inactive_sessions(self, timeout_seconds: float = 300):
        """清理不活跃的会话"""
        current_time = time.time()
        inactive_clients = []
        
        for client_id, session in self.active_sessions.items():
            if current_time - session['last_activity'] > timeout_seconds:
                inactive_clients.append(client_id)
        
        for client_id in inactive_clients:
            self.cleanup_session(client_id)
            logger.info(f"Cleaned up inactive session for {client_id}")
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        success_rate = (
            self.stats.successful_requests / self.stats.total_requests 
            if self.stats.total_requests > 0 else 0
        )
        
        return {
            'total_requests': self.stats.total_requests,
            'successful_requests': self.stats.successful_requests,
            'failed_requests': self.stats.failed_requests,
            'success_rate': success_rate,
            'avg_processing_time': self.stats.avg_processing_time,
            'avg_confidence': self.stats.avg_confidence,
            'languages_detected': self.stats.languages_detected,
            'active_sessions': len(self.active_sessions),
            'last_recognition_time': self.stats.last_recognition_time
        }

class LanguageDetector:
    """简单的语言检测器"""
    
    def __init__(self):
        # 简单的语言特征词典
        self.language_patterns = {
            'en-US': ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with'],
            'zh-CN': ['的', '是', '在', '了', '和', '有', '我', '你', '他', '她'],
            'es-ES': ['el', 'la', 'de', 'que', 'y', 'en', 'un', 'es', 'se', 'no'],
            'fr-FR': ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir'],
            'de-DE': ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich']
        }
    
    def detect_language(self, text: str) -> str:
        """检测文本语言"""
        if not text:
            return 'en-US'  # 默认语言
        
        text_lower = text.lower()
        scores = {}
        
        for lang, patterns in self.language_patterns.items():
            score = sum(1 for pattern in patterns if pattern in text_lower)
            scores[lang] = score
        
        # 返回得分最高的语言
        detected_lang = max(scores, key=scores.get)
        return detected_lang if scores[detected_lang] > 0 else 'en-US'

# 全局优化器实例
speech_optimizer = SpeechRecognitionOptimizer()
