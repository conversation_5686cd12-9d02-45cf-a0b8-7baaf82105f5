# Core FastAPI dependencies (no compilation required)
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-dotenv==1.0.0
python-multipart==0.0.6
pydantic==2.5.0

# Google Cloud dependencies
google-cloud-aiplatform==1.70.0
google-cloud-speech==2.21.0
google-cloud-storage==2.10.0
google-generativeai==0.3.2
google-auth==2.23.4
google-oauth2-tool==0.0.3

# HTTP clients and async support
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Image processing
Pillow>=10.0.0

# Async file handling
aiofiles==23.2.1

# Audio processing
pydub==0.25.1

# File type detection (Windows compatible)
python-magic-bin==0.4.14

# Data processing and utilities
pandas>=2.0.0
numpy>=1.24.0

# Security and authentication
cryptography>=41.0.0

# Additional utilities for API integrations
hashlib-compat==1.0.1

# Connection pooling and circuit breaker
asyncpg==0.29.0
redis==5.0.1
circuitbreaker==1.4.0

# Advanced async utilities
asyncio-throttle==1.0.2
tenacity==8.2.3

# System monitoring
psutil==5.9.6
