# SynTour 代码调试和优化建议

## 概述
基于对SynTour后端系统的全面代码分析，本文档提供具体的性能优化、内存管理和异常处理改进建议。

## 1. 性能瓶颈分析与优化

### 1.1 全局变量和单例模式问题
**问题识别:**
```python
# main.py 中的全局变量
gemini_model = None
continuation_store: Dict[str, Dict[str, Any]] = {}
continuation_timestamps: Dict[str, float] = {}
```

**优化建议:**
- **优先级: 高**
- 使用依赖注入替代全局变量
- 实现单例模式管理器

**实施方案:**
```python
# 新建 app/core/singleton_manager.py
class SingletonManager:
    _instances = {}
    
    @classmethod
    def get_instance(cls, class_name: str, *args, **kwargs):
        if class_name not in cls._instances:
            cls._instances[class_name] = globals()[class_name](*args, **kwargs)
        return cls._instances[class_name]

# 新建 app/core/model_manager.py
class ModelManager:
    def __init__(self):
        self._gemini_model = None
        self._continuation_store = {}
        self._continuation_timestamps = {}
    
    async def initialize_model(self):
        # 模型初始化逻辑
        pass
    
    def get_model(self):
        return self._gemini_model
```

### 1.2 异步操作优化
**问题识别:**
- WebSocket连接管理缺乏连接池
- 文件上传处理阻塞主线程
- API调用缺乏并发控制

**优化建议:**
```python
# 新建 app/core/connection_pool.py
import asyncio
from typing import Dict, Set
import weakref

class ConnectionPool:
    def __init__(self, max_connections: int = 100):
        self.max_connections = max_connections
        self.active_connections: Set[weakref.ref] = set()
        self.semaphore = asyncio.Semaphore(max_connections)
    
    async def acquire_connection(self, websocket):
        await self.semaphore.acquire()
        self.active_connections.add(weakref.ref(websocket))
    
    def release_connection(self, websocket):
        self.semaphore.release()
        # 清理弱引用
        self.active_connections = {ref for ref in self.active_connections if ref() is not None}
```

### 1.3 内存泄漏预防
**问题识别:**
- continuation_store 无限增长
- WebSocket连接未正确清理
- 缓存无过期机制

**优化建议:**
```python
# 改进 continuation_store 管理
import asyncio
from datetime import datetime, timedelta

class ContinuationManager:
    def __init__(self, max_size: int = 1000, ttl_hours: int = 24):
        self.store = {}
        self.timestamps = {}
        self.max_size = max_size
        self.ttl = timedelta(hours=ttl_hours)
        self._cleanup_task = None
    
    async def start_cleanup_task(self):
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def _periodic_cleanup(self):
        while True:
            await asyncio.sleep(3600)  # 每小时清理一次
            await self.cleanup_expired()
    
    async def cleanup_expired(self):
        now = datetime.now()
        expired_keys = [
            key for key, timestamp in self.timestamps.items()
            if now - timestamp > self.ttl
        ]
        for key in expired_keys:
            self.store.pop(key, None)
            self.timestamps.pop(key, None)
```

## 2. 异常处理优化

### 2.1 统一异常处理机制
**当前问题:**
- 异常处理分散在各个模块
- 错误信息不够详细
- 缺乏错误分类和恢复机制

**优化建议:**
```python
# 新建 app/core/exception_handler.py
from enum import Enum
from typing import Optional, Dict, Any
import traceback

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SynTourException(Exception):
    def __init__(
        self, 
        message: str, 
        error_code: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        recoverable: bool = True
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.severity = severity
        self.context = context or {}
        self.recoverable = recoverable
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()

class ExceptionHandler:
    @staticmethod
    async def handle_exception(exc: Exception, context: Dict[str, Any] = None):
        if isinstance(exc, SynTourException):
            return await ExceptionHandler._handle_syntour_exception(exc, context)
        else:
            return await ExceptionHandler._handle_generic_exception(exc, context)
```

### 2.2 API调用异常处理
**优化建议:**
```python
# 改进 app/core/async_error_handler.py
class APICallHandler:
    @staticmethod
    async def safe_api_call(
        api_func,
        *args,
        max_retries: int = 3,
        backoff_factor: float = 1.5,
        timeout: float = 30.0,
        **kwargs
    ):
        for attempt in range(max_retries + 1):
            try:
                return await asyncio.wait_for(
                    api_func(*args, **kwargs),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                if attempt == max_retries:
                    raise SynTourException(
                        f"API call timeout after {max_retries} attempts",
                        "API_TIMEOUT",
                        ErrorSeverity.HIGH
                    )
                await asyncio.sleep(backoff_factor ** attempt)
            except Exception as e:
                if attempt == max_retries:
                    raise SynTourException(
                        f"API call failed: {str(e)}",
                        "API_ERROR",
                        ErrorSeverity.HIGH,
                        context={"original_error": str(e), "attempts": attempt + 1}
                    )
                await asyncio.sleep(backoff_factor ** attempt)
```

## 3. 数据库操作优化

### 3.1 连接池管理
**当前问题:**
- 缺乏数据库连接池
- 无连接超时管理

**优化建议:**
```python
# 新建 app/core/database.py
import asyncpg
from typing import Optional

class DatabaseManager:
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
    
    async def initialize(
        self,
        database_url: str,
        min_size: int = 10,
        max_size: int = 20,
        command_timeout: int = 60
    ):
        self.pool = await asyncpg.create_pool(
            database_url,
            min_size=min_size,
            max_size=max_size,
            command_timeout=command_timeout,
            server_settings={
                'jit': 'off'  # 禁用JIT以提高小查询性能
            }
        )
    
    async def execute_query(self, query: str, *args):
        async with self.pool.acquire() as connection:
            return await connection.fetch(query, *args)
    
    async def close(self):
        if self.pool:
            await self.pool.close()
```

## 4. 监控和日志优化

### 4.1 性能监控
**优化建议:**
```python
# 新建 app/core/performance_monitor.py
import time
import psutil
from functools import wraps

class PerformanceMonitor:
    @staticmethod
    def monitor_performance(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                end_memory = psutil.Process().memory_info().rss
                
                execution_time = end_time - start_time
                memory_delta = end_memory - start_memory
                
                logger.info(f"Function {func.__name__} - "
                           f"Time: {execution_time:.3f}s, "
                           f"Memory: {memory_delta / 1024 / 1024:.2f}MB")
        
        return wrapper
```

## 5. 实施优先级

### 高优先级 (立即实施)
1. 全局变量重构
2. continuation_store 内存泄漏修复
3. 统一异常处理机制

### 中优先级 (1-2周内)
1. 异步操作优化
2. 连接池实现
3. 性能监控系统

### 低优先级 (长期优化)
1. 数据库查询优化
2. 缓存策略改进
3. 日志系统升级

## 6. 测试建议

### 6.1 性能测试
```python
# tests/performance/test_memory_usage.py
import pytest
import psutil
import asyncio

@pytest.mark.asyncio
async def test_memory_leak_prevention():
    initial_memory = psutil.Process().memory_info().rss
    
    # 模拟大量请求
    for _ in range(1000):
        # 执行API调用
        pass
    
    final_memory = psutil.Process().memory_info().rss
    memory_increase = (final_memory - initial_memory) / 1024 / 1024
    
    assert memory_increase < 50, f"Memory increased by {memory_increase}MB"
```

### 6.2 并发测试
```python
# tests/performance/test_concurrency.py
@pytest.mark.asyncio
async def test_concurrent_api_calls():
    tasks = []
    for _ in range(100):
        task = asyncio.create_task(api_call_function())
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 检查成功率
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    success_rate = success_count / len(results)
    
    assert success_rate > 0.95, f"Success rate: {success_rate}"
```

## 总结

这些优化建议将显著提升SynTour后端系统的性能、稳定性和可维护性。建议按照优先级逐步实施，并在每个阶段进行充分的测试验证。
