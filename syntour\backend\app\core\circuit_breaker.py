# app/core/circuit_breaker.py
import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, Union, List
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
import json

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, requests fail fast
    HALF_OPEN = "half_open"  # Testing if service is back

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5  # Number of failures before opening
    recovery_timeout: float = 60.0  # Seconds before trying half-open
    expected_exception: tuple = (Exception,)  # Exceptions that count as failures
    success_threshold: int = 3  # Successful calls needed to close from half-open
    timeout: float = 30.0  # Request timeout
    fallback_response: Any = None  # Default response when circuit is open
    name: str = "default"

@dataclass
class CircuitBreakerStats:
    """Statistics for circuit breaker"""
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    success_count: int = 0
    total_requests: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    state_change_time: float = field(default_factory=time.time)
    open_count: int = 0  # Number of times circuit has opened

class CircuitBreaker:
    """Advanced circuit breaker with fallback mechanisms"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.stats = CircuitBreakerStats()
        self._lock = asyncio.Lock()
        self._fallback_handlers: List[Callable] = []
        
    def add_fallback_handler(self, handler: Callable):
        """Add a fallback handler for when circuit is open"""
        self._fallback_handlers.append(handler)
    
    async def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit"""
        if self.stats.state != CircuitState.OPEN:
            return False
        
        if self.stats.last_failure_time is None:
            return False
        
        return (time.time() - self.stats.last_failure_time) >= self.config.recovery_timeout
    
    async def _record_success(self):
        """Record a successful operation"""
        async with self._lock:
            self.stats.success_count += 1
            self.stats.total_requests += 1
            self.stats.last_success_time = time.time()
            
            if self.stats.state == CircuitState.HALF_OPEN:
                if self.stats.success_count >= self.config.success_threshold:
                    await self._close_circuit()
            elif self.stats.state == CircuitState.OPEN:
                # This shouldn't happen, but handle it
                await self._close_circuit()
    
    async def _record_failure(self, exception: Exception):
        """Record a failed operation"""
        async with self._lock:
            self.stats.failure_count += 1
            self.stats.total_requests += 1
            self.stats.last_failure_time = time.time()
            
            logger.warning(f"Circuit breaker '{self.config.name}' recorded failure: {exception}")
            
            if self.stats.state == CircuitState.CLOSED:
                if self.stats.failure_count >= self.config.failure_threshold:
                    await self._open_circuit()
            elif self.stats.state == CircuitState.HALF_OPEN:
                await self._open_circuit()
    
    async def _open_circuit(self):
        """Open the circuit"""
        self.stats.state = CircuitState.OPEN
        self.stats.state_change_time = time.time()
        self.stats.open_count += 1
        self.stats.failure_count = 0  # Reset for next cycle
        
        logger.error(f"Circuit breaker '{self.config.name}' opened after {self.config.failure_threshold} failures")
    
    async def _close_circuit(self):
        """Close the circuit"""
        self.stats.state = CircuitState.CLOSED
        self.stats.state_change_time = time.time()
        self.stats.failure_count = 0
        self.stats.success_count = 0
        
        logger.info(f"Circuit breaker '{self.config.name}' closed - service recovered")
    
    async def _half_open_circuit(self):
        """Set circuit to half-open state"""
        self.stats.state = CircuitState.HALF_OPEN
        self.stats.state_change_time = time.time()
        self.stats.success_count = 0
        
        logger.info(f"Circuit breaker '{self.config.name}' half-opened - testing service")
    
    async def _execute_fallback(self, *args, **kwargs):
        """Execute fallback handlers"""
        for handler in self._fallback_handlers:
            try:
                result = await handler(*args, **kwargs) if asyncio.iscoroutinefunction(handler) else handler(*args, **kwargs)
                logger.info(f"Circuit breaker '{self.config.name}' fallback executed successfully")
                return result
            except Exception as e:
                logger.error(f"Fallback handler failed: {e}")
                continue
        
        # If no fallback handlers or all failed, return default
        if self.config.fallback_response is not None:
            return self.config.fallback_response
        
        raise CircuitBreakerOpenError(f"Circuit breaker '{self.config.name}' is open and no fallback available")
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        # Check if we should attempt reset
        if await self._should_attempt_reset():
            await self._half_open_circuit()
        
        # If circuit is open, execute fallback
        if self.stats.state == CircuitState.OPEN:
            return await self._execute_fallback(*args, **kwargs)
        
        # Execute the function
        try:
            # Apply timeout if specified
            if self.config.timeout:
                result = await asyncio.wait_for(
                    func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs),
                    timeout=self.config.timeout
                )
            else:
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            await self._record_success()
            return result
            
        except self.config.expected_exception as e:
            await self._record_failure(e)
            raise
        except asyncio.TimeoutError as e:
            await self._record_failure(e)
            raise CircuitBreakerTimeoutError(f"Function timed out after {self.config.timeout}s")
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get current circuit breaker statistics"""
        return self.stats
    
    def is_closed(self) -> bool:
        """Check if circuit is closed (normal operation)"""
        return self.stats.state == CircuitState.CLOSED
    
    def is_open(self) -> bool:
        """Check if circuit is open (failing fast)"""
        return self.stats.state == CircuitState.OPEN
    
    def is_half_open(self) -> bool:
        """Check if circuit is half-open (testing)"""
        return self.stats.state == CircuitState.HALF_OPEN

class CircuitBreakerOpenError(Exception):
    """Exception raised when circuit breaker is open"""
    pass

class CircuitBreakerTimeoutError(Exception):
    """Exception raised when circuit breaker times out"""
    pass

class CircuitBreakerManager:
    """Manages multiple circuit breakers"""
    
    def __init__(self):
        self._breakers: Dict[str, CircuitBreaker] = {}
    
    def create_breaker(self, name: str, config: CircuitBreakerConfig) -> CircuitBreaker:
        """Create a new circuit breaker"""
        config.name = name
        breaker = CircuitBreaker(config)
        self._breakers[name] = breaker
        return breaker
    
    def get_breaker(self, name: str) -> Optional[CircuitBreaker]:
        """Get an existing circuit breaker"""
        return self._breakers.get(name)
    
    def get_all_stats(self) -> Dict[str, CircuitBreakerStats]:
        """Get statistics for all circuit breakers"""
        return {name: breaker.get_stats() for name, breaker in self._breakers.items()}
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get health summary of all circuit breakers"""
        summary = {
            "total_breakers": len(self._breakers),
            "healthy": 0,
            "degraded": 0,
            "unhealthy": 0,
            "breakers": {}
        }
        
        for name, breaker in self._breakers.items():
            stats = breaker.get_stats()
            
            if stats.state == CircuitState.CLOSED:
                summary["healthy"] += 1
                health = "healthy"
            elif stats.state == CircuitState.HALF_OPEN:
                summary["degraded"] += 1
                health = "degraded"
            else:
                summary["unhealthy"] += 1
                health = "unhealthy"
            
            summary["breakers"][name] = {
                "state": stats.state.value,
                "health": health,
                "failure_count": stats.failure_count,
                "success_count": stats.success_count,
                "total_requests": stats.total_requests,
                "open_count": stats.open_count
            }
        
        return summary

# Decorator for easy circuit breaker usage
def circuit_breaker(config: CircuitBreakerConfig):
    """Decorator to add circuit breaker protection to functions"""
    def decorator(func):
        breaker = CircuitBreaker(config)
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await breaker.call(func, *args, **kwargs)
        
        # Attach breaker to function for access to stats
        wrapper.circuit_breaker = breaker
        return wrapper
    
    return decorator

# Global circuit breaker manager instance
circuit_breaker_manager = CircuitBreakerManager()

# Predefined circuit breaker configurations
API_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=5,
    recovery_timeout=60.0,
    success_threshold=3,
    timeout=30.0,
    name="api_default"
)

DATABASE_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=30.0,
    success_threshold=2,
    timeout=10.0,
    name="database_default"
)

AI_MODEL_CIRCUIT_BREAKER_CONFIG = CircuitBreakerConfig(
    failure_threshold=3,
    recovery_timeout=120.0,
    success_threshold=2,
    timeout=60.0,
    name="ai_model_default"
)
