# app/core/response_formatter.py
import json
import uuid
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field

class ResponseStatus(str, Enum):
    """Standard response status values"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"

class ResponseType(str, Enum):
    """Types of responses"""
    AI_CHAT = "ai_chat"
    API_DATA = "api_data"
    HEALTH_CHECK = "health_check"
    ERROR_RESPONSE = "error_response"
    MULTIMODAL = "multimodal"
    TRAVEL_PLAN = "travel_plan"

class ErrorCode(str, Enum):
    """Standardized error codes"""
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    AUTHORIZATION_ERROR = "AUTHORIZATION_ERROR"
    NOT_FOUND = "NOT_FOUND"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    EXTERNAL_API_ERROR = "EXTERNAL_API_ERROR"
    AI_MODEL_ERROR = "AI_MODEL_ERROR"
    DATABASE_ERROR = "DATABASE_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    PROCESSING_ERROR = "PROCESSING_ERROR"

@dataclass
class ErrorDetail:
    """Detailed error information"""
    code: ErrorCode
    message: str
    field: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResponseMetadata:
    """Metadata for responses"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    version: str = "2.0"
    response_type: ResponseType = ResponseType.API_DATA
    processing_time_ms: Optional[float] = None
    source: Optional[str] = None
    cache_hit: bool = False
    rate_limit_remaining: Optional[int] = None
    rate_limit_reset: Optional[str] = None

@dataclass
class PaginationInfo:
    """Pagination information"""
    page: int
    page_size: int
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool
    next_page: Optional[int] = None
    previous_page: Optional[int] = None

class StandardResponse(BaseModel):
    """Standard response format for all API endpoints"""
    status: ResponseStatus
    data: Optional[Any] = None
    errors: List[ErrorDetail] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    metadata: ResponseMetadata
    pagination: Optional[PaginationInfo] = None
    
    # AI-specific fields
    ai_model: Optional[str] = None
    confidence_score: Optional[float] = None
    is_truncated: bool = False
    continuation_token: Optional[str] = None
    
    # Backward compatibility fields
    success: Optional[bool] = None
    message: Optional[str] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        # Automatically set backward compatibility fields
        self.success = self.status == ResponseStatus.SUCCESS
        if self.data and isinstance(self.data, str):
            self.message = self.data

class ResponseFormatter:
    """Advanced response formatter with templating and internationalization"""
    
    def __init__(self, version: str = "2.0", language: str = "en"):
        self.version = version
        self.language = language
        self.templates: Dict[str, Dict[str, str]] = {}
        self.translations: Dict[str, Dict[str, str]] = {}
        
        # Load default templates and translations
        self._load_default_templates()
        self._load_default_translations()
    
    def _load_default_templates(self):
        """Load default response templates"""
        self.templates = {
            "ai_chat": {
                "success": "AI response generated successfully",
                "error": "Failed to generate AI response",
                "timeout": "AI response generation timed out",
                "rate_limit": "AI service rate limit exceeded"
            },
            "api_data": {
                "success": "Data retrieved successfully",
                "error": "Failed to retrieve data",
                "not_found": "Requested data not found",
                "validation_error": "Invalid request parameters"
            },
            "health_check": {
                "healthy": "Service is healthy",
                "degraded": "Service is degraded",
                "unhealthy": "Service is unhealthy"
            },
            "travel_plan": {
                "success": "Travel plan generated successfully",
                "error": "Failed to generate travel plan",
                "partial": "Travel plan partially generated"
            }
        }
    
    def _load_default_translations(self):
        """Load default translations"""
        self.translations = {
            "en": {
                "success": "Success",
                "error": "Error",
                "warning": "Warning",
                "processing": "Processing",
                "timeout": "Request timed out",
                "rate_limit": "Rate limit exceeded",
                "not_found": "Not found",
                "validation_error": "Validation error",
                "internal_error": "Internal server error",
                "service_unavailable": "Service temporarily unavailable"
            },
            "zh": {
                "success": "成功",
                "error": "错误",
                "warning": "警告",
                "processing": "处理中",
                "timeout": "请求超时",
                "rate_limit": "请求频率超限",
                "not_found": "未找到",
                "validation_error": "验证错误",
                "internal_error": "内部服务器错误",
                "service_unavailable": "服务暂时不可用"
            },
            "es": {
                "success": "Éxito",
                "error": "Error",
                "warning": "Advertencia",
                "processing": "Procesando",
                "timeout": "Tiempo de espera agotado",
                "rate_limit": "Límite de velocidad excedido",
                "not_found": "No encontrado",
                "validation_error": "Error de validación",
                "internal_error": "Error interno del servidor",
                "service_unavailable": "Servicio temporalmente no disponible"
            }
        }
    
    def _translate(self, key: str, language: Optional[str] = None) -> str:
        """Translate a message key"""
        lang = language or self.language
        return self.translations.get(lang, {}).get(key, key)
    
    def _get_template_message(self, response_type: str, template_key: str) -> str:
        """Get message from template"""
        return self.templates.get(response_type, {}).get(template_key, template_key)
    
    def format_ai_response(self,
                          content: str,
                          model_name: str = "gemini-pro",
                          confidence: Optional[float] = None,
                          is_truncated: bool = False,
                          continuation_token: Optional[str] = None,
                          processing_time: Optional[float] = None,
                          request_id: Optional[str] = None,
                          language: Optional[str] = None) -> StandardResponse:
        """Format AI chat response"""
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.AI_CHAT,
            processing_time_ms=processing_time,
            source=model_name
        )
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=content,
            metadata=metadata,
            ai_model=model_name,
            confidence_score=confidence,
            is_truncated=is_truncated,
            continuation_token=continuation_token
        )
    
    def format_api_data(self,
                       data: Any,
                       api_source: str,
                       processing_time: Optional[float] = None,
                       cache_hit: bool = False,
                       pagination: Optional[PaginationInfo] = None,
                       request_id: Optional[str] = None) -> StandardResponse:
        """Format API data response"""
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.API_DATA,
            processing_time_ms=processing_time,
            source=api_source,
            cache_hit=cache_hit
        )
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=data,
            metadata=metadata,
            pagination=pagination
        )
    
    def format_error(self,
                    error_code: ErrorCode,
                    message: str,
                    details: Optional[Dict[str, Any]] = None,
                    field: Optional[str] = None,
                    request_id: Optional[str] = None,
                    language: Optional[str] = None) -> StandardResponse:
        """Format error response"""
        
        # Translate error message if needed
        translated_message = self._translate(message.lower(), language) if language else message
        
        error_detail = ErrorDetail(
            code=error_code,
            message=translated_message,
            field=field,
            details=details or {}
        )
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.ERROR_RESPONSE
        )
        
        return StandardResponse(
            status=ResponseStatus.ERROR,
            errors=[error_detail],
            metadata=metadata
        )
    
    def format_health_check(self,
                           status: str,
                           checks: Dict[str, Any],
                           system_metrics: Optional[Dict[str, Any]] = None,
                           request_id: Optional[str] = None) -> StandardResponse:
        """Format health check response"""
        
        # Determine response status
        if status == "healthy":
            response_status = ResponseStatus.SUCCESS
        elif status == "degraded":
            response_status = ResponseStatus.WARNING
        else:
            response_status = ResponseStatus.ERROR
        
        data = {
            "overall_status": status,
            "checks": checks
        }
        
        if system_metrics:
            data["system_metrics"] = system_metrics
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.HEALTH_CHECK,
            source="health_monitor"
        )
        
        return StandardResponse(
            status=response_status,
            data=data,
            metadata=metadata
        )
    
    def format_multimodal_response(self,
                                  content: str,
                                  processed_files: List[Dict[str, Any]],
                                  model_name: str = "gemini-pro-vision",
                                  confidence: Optional[float] = None,
                                  processing_time: Optional[float] = None,
                                  request_id: Optional[str] = None) -> StandardResponse:
        """Format multimodal AI response"""
        
        data = {
            "response": content,
            "processed_files": processed_files,
            "file_count": len(processed_files)
        }
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.MULTIMODAL,
            processing_time_ms=processing_time,
            source=model_name
        )
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=data,
            metadata=metadata,
            ai_model=model_name,
            confidence_score=confidence
        )
    
    def format_travel_plan(self,
                          plan: Dict[str, Any],
                          destinations: List[str],
                          duration: str,
                          model_name: str = "gemini-pro",
                          processing_time: Optional[float] = None,
                          request_id: Optional[str] = None) -> StandardResponse:
        """Format travel plan response"""
        
        data = {
            "travel_plan": plan,
            "destinations": destinations,
            "duration": duration,
            "generated_at": datetime.now().isoformat()
        }
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.TRAVEL_PLAN,
            processing_time_ms=processing_time,
            source=model_name
        )
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=data,
            metadata=metadata,
            ai_model=model_name
        )
    
    def format_validation_error(self,
                               validation_errors: List[Dict[str, str]],
                               request_id: Optional[str] = None,
                               language: Optional[str] = None) -> StandardResponse:
        """Format validation error response"""
        
        errors = []
        for error in validation_errors:
            errors.append(ErrorDetail(
                code=ErrorCode.VALIDATION_ERROR,
                message=self._translate(error.get("message", ""), language),
                field=error.get("field"),
                details=error.get("details", {})
            ))
        
        metadata = ResponseMetadata(
            request_id=request_id or str(uuid.uuid4()),
            version=self.version,
            response_type=ResponseType.ERROR_RESPONSE
        )
        
        return StandardResponse(
            status=ResponseStatus.ERROR,
            errors=errors,
            metadata=metadata
        )
    
    def add_template(self, response_type: str, templates: Dict[str, str]):
        """Add custom templates"""
        if response_type not in self.templates:
            self.templates[response_type] = {}
        self.templates[response_type].update(templates)
    
    def add_translations(self, language: str, translations: Dict[str, str]):
        """Add custom translations"""
        if language not in self.translations:
            self.translations[language] = {}
        self.translations[language].update(translations)
    
    def set_language(self, language: str):
        """Set default language for responses"""
        self.language = language

# Global response formatter instance
response_formatter = ResponseFormatter()

# Utility functions for backward compatibility
def create_success_response(data: Any, message: str = "Success") -> Dict[str, Any]:
    """Create a simple success response (backward compatibility)"""
    return {
        "success": True,
        "data": data,
        "message": message,
        "timestamp": datetime.now().isoformat()
    }

def create_error_response(error: str, code: Optional[str] = None) -> Dict[str, Any]:
    """Create a simple error response (backward compatibility)"""
    return {
        "success": False,
        "error": error,
        "code": code,
        "timestamp": datetime.now().isoformat()
    }

class ResponseTemplateEngine:
    """Template engine for dynamic response generation"""

    def __init__(self):
        self.templates: Dict[str, str] = {}
        self.template_variables: Dict[str, Dict[str, Any]] = {}
        self._load_default_templates()

    def _load_default_templates(self):
        """Load default response templates"""
        self.templates = {
            "ai_chat_success": """
            {
                "status": "success",
                "data": "{{content}}",
                "metadata": {
                    "request_id": "{{request_id}}",
                    "timestamp": "{{timestamp}}",
                    "response_type": "ai_chat",
                    "processing_time_ms": {{processing_time}},
                    "source": "{{model_name}}"
                },
                "ai_model": "{{model_name}}",
                "confidence_score": {{confidence}},
                "is_truncated": {{is_truncated}}
            }
            """,

            "api_data_success": """
            {
                "status": "success",
                "data": {{data}},
                "metadata": {
                    "request_id": "{{request_id}}",
                    "timestamp": "{{timestamp}}",
                    "response_type": "api_data",
                    "processing_time_ms": {{processing_time}},
                    "source": "{{api_source}}",
                    "cache_hit": {{cache_hit}}
                }
            }
            """,

            "error_response": """
            {
                "status": "error",
                "errors": [
                    {
                        "code": "{{error_code}}",
                        "message": "{{error_message}}",
                        "field": "{{field}}",
                        "details": {{error_details}}
                    }
                ],
                "metadata": {
                    "request_id": "{{request_id}}",
                    "timestamp": "{{timestamp}}",
                    "response_type": "error_response"
                }
            }
            """,

            "health_check": """
            {
                "status": "{{health_status}}",
                "data": {
                    "overall_status": "{{overall_status}}",
                    "checks": {{checks}},
                    "system_metrics": {{system_metrics}}
                },
                "metadata": {
                    "request_id": "{{request_id}}",
                    "timestamp": "{{timestamp}}",
                    "response_type": "health_check",
                    "source": "health_monitor"
                }
            }
            """
        }

    def render_template(self, template_name: str, variables: Dict[str, Any]) -> str:
        """Render a template with variables"""
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")

        template = self.templates[template_name]

        # Simple template variable substitution
        for key, value in variables.items():
            placeholder = f"{{{{{key}}}}}"
            if isinstance(value, str):
                template = template.replace(placeholder, value)
            else:
                template = template.replace(placeholder, json.dumps(value))

        return template

    def add_template(self, name: str, template: str):
        """Add a custom template"""
        self.templates[name] = template

    def get_template(self, name: str) -> Optional[str]:
        """Get a template by name"""
        return self.templates.get(name)

    def list_templates(self) -> List[str]:
        """List all available templates"""
        return list(self.templates.keys())

# Global template engine instance
template_engine = ResponseTemplateEngine()
