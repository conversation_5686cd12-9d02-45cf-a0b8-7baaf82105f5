# SynTour AI输出长度控制建议

## 概述
基于对SynTour AI系统输出长度的分析，本文档提供动态长度控制机制、内容摘要策略和分页方案，以优化用户体验和系统性能。

## 1. 当前长度控制分析

### 1.1 现有机制问题
**当前实现:**
```python
# main.py 中的简单截断机制
continuation_store: Dict[str, Dict[str, Any]] = {}
continuation_timestamps: Dict[str, float] = {}

# 固定的token限制
config = genai.types.GenerateContentConfig(
    temperature=0.7,
    top_p=0.9,
    max_output_tokens=4096,  # 固定限制
)
```

**问题识别:**
- 固定的token限制不够灵活
- 缺乏基于内容类型的动态调整
- 简单的截断机制用户体验差
- 没有考虑设备类型和网络条件
- 缺乏智能摘要功能

### 1.2 长度分布分析
**需要分析的维度:**
- 不同查询类型的响应长度分布
- 用户设备类型对长度偏好的影响
- 网络条件对响应时间的影响
- 用户满意度与响应长度的关系

## 2. 动态长度控制机制

### 2.1 智能长度控制器
**优化建议:**
```python
# 新建 app/core/length_controller.py
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass
import re

class ContentType(Enum):
    SIMPLE_ANSWER = "simple_answer"
    TRAVEL_PLAN = "travel_plan"
    DESTINATION_INFO = "destination_info"
    ITINERARY = "itinerary"
    RECOMMENDATIONS = "recommendations"
    EXPLANATION = "explanation"

class DeviceType(Enum):
    MOBILE = "mobile"
    TABLET = "tablet"
    DESKTOP = "desktop"
    UNKNOWN = "unknown"

class NetworkCondition(Enum):
    FAST = "fast"
    MEDIUM = "medium"
    SLOW = "slow"
    UNKNOWN = "unknown"

@dataclass
class LengthConstraints:
    """长度约束配置"""
    min_tokens: int
    max_tokens: int
    preferred_tokens: int
    max_sentences: Optional[int] = None
    max_paragraphs: Optional[int] = None
    enable_truncation: bool = True
    enable_summarization: bool = False

@dataclass
class UserContext:
    """用户上下文"""
    device_type: DeviceType = DeviceType.UNKNOWN
    network_condition: NetworkCondition = NetworkCondition.UNKNOWN
    user_preference: Optional[str] = None  # "brief", "detailed", "comprehensive"
    session_history: List[str] = None
    time_constraint: Optional[int] = None  # 用户期望的响应时间（秒）

class LengthController:
    """智能长度控制器"""
    
    def __init__(self):
        # 基础长度配置
        self.base_constraints = {
            ContentType.SIMPLE_ANSWER: LengthConstraints(
                min_tokens=50, max_tokens=200, preferred_tokens=100,
                max_sentences=3, enable_summarization=False
            ),
            ContentType.TRAVEL_PLAN: LengthConstraints(
                min_tokens=500, max_tokens=2000, preferred_tokens=1000,
                max_paragraphs=8, enable_summarization=True
            ),
            ContentType.DESTINATION_INFO: LengthConstraints(
                min_tokens=200, max_tokens=800, preferred_tokens=400,
                max_paragraphs=5, enable_summarization=True
            ),
            ContentType.ITINERARY: LengthConstraints(
                min_tokens=300, max_tokens=1500, preferred_tokens=800,
                max_paragraphs=10, enable_summarization=True
            ),
            ContentType.RECOMMENDATIONS: LengthConstraints(
                min_tokens=150, max_tokens=600, preferred_tokens=300,
                max_sentences=10, enable_summarization=False
            ),
            ContentType.EXPLANATION: LengthConstraints(
                min_tokens=100, max_tokens=500, preferred_tokens=250,
                max_paragraphs=3, enable_summarization=True
            )
        }
        
        # 设备类型调整因子
        self.device_factors = {
            DeviceType.MOBILE: 0.7,     # 移动设备偏好较短内容
            DeviceType.TABLET: 0.9,     # 平板设备中等长度
            DeviceType.DESKTOP: 1.2,    # 桌面设备可接受较长内容
            DeviceType.UNKNOWN: 1.0
        }
        
        # 网络条件调整因子
        self.network_factors = {
            NetworkCondition.SLOW: 0.6,    # 慢网络需要更短内容
            NetworkCondition.MEDIUM: 0.8,
            NetworkCondition.FAST: 1.0,
            NetworkCondition.UNKNOWN: 0.9
        }
    
    def determine_content_type(self, query: str, context: Dict[str, Any] = None) -> ContentType:
        """确定内容类型"""
        query_lower = query.lower()
        
        # 简单问答关键词
        simple_keywords = ["what", "where", "when", "who", "how much", "is", "are"]
        if any(keyword in query_lower for keyword in simple_keywords) and len(query.split()) < 10:
            return ContentType.SIMPLE_ANSWER
        
        # 旅游规划关键词
        plan_keywords = ["plan", "itinerary", "trip", "vacation", "holiday", "travel plan"]
        if any(keyword in query_lower for keyword in plan_keywords):
            return ContentType.TRAVEL_PLAN
        
        # 目的地信息关键词
        destination_keywords = ["about", "information", "tell me about", "describe"]
        if any(keyword in query_lower for keyword in destination_keywords):
            return ContentType.DESTINATION_INFO
        
        # 推荐关键词
        recommendation_keywords = ["recommend", "suggest", "best", "top", "should i"]
        if any(keyword in query_lower for keyword in recommendation_keywords):
            return ContentType.RECOMMENDATIONS
        
        # 解释关键词
        explanation_keywords = ["why", "how", "explain", "because"]
        if any(keyword in query_lower for keyword in explanation_keywords):
            return ContentType.EXPLANATION
        
        # 默认返回目的地信息
        return ContentType.DESTINATION_INFO
    
    def calculate_optimal_length(
        self,
        content_type: ContentType,
        user_context: UserContext
    ) -> LengthConstraints:
        """计算最优长度约束"""
        base_constraints = self.base_constraints[content_type]
        
        # 应用设备类型调整
        device_factor = self.device_factors[user_context.device_type]
        
        # 应用网络条件调整
        network_factor = self.network_factors[user_context.network_condition]
        
        # 应用用户偏好调整
        preference_factor = self._get_preference_factor(user_context.user_preference)
        
        # 计算最终因子
        total_factor = device_factor * network_factor * preference_factor
        
        # 应用调整
        adjusted_constraints = LengthConstraints(
            min_tokens=max(50, int(base_constraints.min_tokens * total_factor)),
            max_tokens=int(base_constraints.max_tokens * total_factor),
            preferred_tokens=int(base_constraints.preferred_tokens * total_factor),
            max_sentences=base_constraints.max_sentences,
            max_paragraphs=base_constraints.max_paragraphs,
            enable_truncation=base_constraints.enable_truncation,
            enable_summarization=base_constraints.enable_summarization
        )
        
        return adjusted_constraints
    
    def _get_preference_factor(self, preference: Optional[str]) -> float:
        """获取用户偏好调整因子"""
        if not preference:
            return 1.0
        
        preference_factors = {
            "brief": 0.6,
            "detailed": 1.2,
            "comprehensive": 1.5
        }
        
        return preference_factors.get(preference.lower(), 1.0)
    
    def should_enable_streaming(
        self,
        content_type: ContentType,
        user_context: UserContext
    ) -> bool:
        """判断是否应该启用流式响应"""
        # 长内容类型建议使用流式响应
        long_content_types = [ContentType.TRAVEL_PLAN, ContentType.ITINERARY]
        
        if content_type in long_content_types:
            return True
        
        # 慢网络条件下建议使用流式响应
        if user_context.network_condition == NetworkCondition.SLOW:
            return True
        
        # 移动设备建议使用流式响应以提升体验
        if user_context.device_type == DeviceType.MOBILE:
            return True
        
        return False
```

### 2.2 内容分段和摘要
**优化建议:**
```python
# 新建 app/core/content_processor.py
import re
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

@dataclass
class ContentSegment:
    """内容段落"""
    content: str
    segment_type: str  # "introduction", "main", "details", "conclusion"
    priority: int      # 1-5, 5为最高优先级
    token_count: int
    can_summarize: bool = True

class ContentProcessor:
    """内容处理器"""
    
    def __init__(self):
        self.summarizer = ContentSummarizer()
        self.segmenter = ContentSegmenter()
    
    def process_content(
        self,
        content: str,
        constraints: LengthConstraints,
        enable_streaming: bool = False
    ) -> Dict[str, Any]:
        """处理内容以满足长度约束"""
        
        # 分段
        segments = self.segmenter.segment_content(content)
        
        # 计算当前长度
        total_tokens = sum(seg.token_count for seg in segments)
        
        if total_tokens <= constraints.max_tokens:
            # 内容在限制范围内，直接返回
            return {
                "content": content,
                "is_truncated": False,
                "segments": segments,
                "total_tokens": total_tokens,
                "processing_applied": "none"
            }
        
        # 需要处理长度
        if constraints.enable_summarization:
            # 尝试摘要
            processed_content = self._apply_summarization(segments, constraints)
        else:
            # 智能截断
            processed_content = self._apply_smart_truncation(segments, constraints)
        
        return processed_content
    
    def _apply_summarization(
        self,
        segments: List[ContentSegment],
        constraints: LengthConstraints
    ) -> Dict[str, Any]:
        """应用摘要策略"""
        
        # 按优先级排序段落
        sorted_segments = sorted(segments, key=lambda x: x.priority, reverse=True)
        
        result_segments = []
        current_tokens = 0
        
        for segment in sorted_segments:
            if current_tokens + segment.token_count <= constraints.preferred_tokens:
                # 直接添加
                result_segments.append(segment)
                current_tokens += segment.token_count
            elif segment.can_summarize and segment.priority >= 3:
                # 摘要后添加
                summarized = self.summarizer.summarize_segment(
                    segment,
                    target_tokens=min(
                        segment.token_count // 2,
                        constraints.preferred_tokens - current_tokens
                    )
                )
                if summarized:
                    result_segments.append(summarized)
                    current_tokens += summarized.token_count
        
        # 重新组合内容
        final_content = self._reconstruct_content(result_segments)
        
        return {
            "content": final_content,
            "is_truncated": len(result_segments) < len(segments),
            "segments": result_segments,
            "total_tokens": current_tokens,
            "processing_applied": "summarization"
        }
    
    def _apply_smart_truncation(
        self,
        segments: List[ContentSegment],
        constraints: LengthConstraints
    ) -> Dict[str, Any]:
        """应用智能截断"""
        
        # 保留高优先级段落
        essential_segments = [seg for seg in segments if seg.priority >= 4]
        optional_segments = [seg for seg in segments if seg.priority < 4]
        
        result_segments = essential_segments.copy()
        current_tokens = sum(seg.token_count for seg in essential_segments)
        
        # 添加可选段落直到达到限制
        for segment in optional_segments:
            if current_tokens + segment.token_count <= constraints.max_tokens:
                result_segments.append(segment)
                current_tokens += segment.token_count
            else:
                break
        
        final_content = self._reconstruct_content(result_segments)
        
        return {
            "content": final_content,
            "is_truncated": True,
            "segments": result_segments,
            "total_tokens": current_tokens,
            "processing_applied": "smart_truncation",
            "continuation_available": len(optional_segments) > len([s for s in result_segments if s in optional_segments])
        }

class ContentSummarizer:
    """内容摘要器"""
    
    def summarize_segment(
        self,
        segment: ContentSegment,
        target_tokens: int
    ) -> Optional[ContentSegment]:
        """摘要段落内容"""
        
        if segment.token_count <= target_tokens:
            return segment
        
        # 简单的摘要策略：保留关键句子
        sentences = self._split_sentences(segment.content)
        
        # 计算句子重要性
        sentence_scores = self._calculate_sentence_importance(sentences)
        
        # 选择最重要的句子
        selected_sentences = self._select_top_sentences(
            sentences, sentence_scores, target_tokens
        )
        
        if not selected_sentences:
            return None
        
        summarized_content = " ".join(selected_sentences)
        
        return ContentSegment(
            content=summarized_content,
            segment_type=segment.segment_type,
            priority=segment.priority,
            token_count=len(summarized_content.split()),  # 简单的token计算
            can_summarize=False  # 已经摘要过，不再摘要
        )
    
    def _split_sentences(self, text: str) -> List[str]:
        """分割句子"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _calculate_sentence_importance(self, sentences: List[str]) -> List[float]:
        """计算句子重要性"""
        scores = []
        
        for sentence in sentences:
            score = 0.0
            
            # 长度因子（中等长度句子得分更高）
            length = len(sentence.split())
            if 10 <= length <= 25:
                score += 0.3
            
            # 关键词因子
            travel_keywords = [
                "recommend", "suggest", "best", "important", "must",
                "popular", "famous", "beautiful", "amazing", "perfect"
            ]
            
            for keyword in travel_keywords:
                if keyword in sentence.lower():
                    score += 0.2
            
            # 位置因子（开头和结尾的句子更重要）
            position_factor = 0.1
            score += position_factor
            
            scores.append(score)
        
        return scores
    
    def _select_top_sentences(
        self,
        sentences: List[str],
        scores: List[float],
        target_tokens: int
    ) -> List[str]:
        """选择最重要的句子"""
        
        # 按分数排序
        sentence_score_pairs = list(zip(sentences, scores))
        sentence_score_pairs.sort(key=lambda x: x[1], reverse=True)
        
        selected = []
        current_tokens = 0
        
        for sentence, score in sentence_score_pairs:
            sentence_tokens = len(sentence.split())
            if current_tokens + sentence_tokens <= target_tokens:
                selected.append(sentence)
                current_tokens += sentence_tokens
            else:
                break
        
        # 按原始顺序重新排列
        original_order_selected = []
        for sentence in sentences:
            if sentence in selected:
                original_order_selected.append(sentence)
        
        return original_order_selected

class ContentSegmenter:
    """内容分段器"""
    
    def segment_content(self, content: str) -> List[ContentSegment]:
        """将内容分段"""
        
        # 按段落分割
        paragraphs = content.split('\n\n')
        segments = []
        
        for i, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                continue
            
            segment_type = self._determine_segment_type(paragraph, i, len(paragraphs))
            priority = self._calculate_segment_priority(paragraph, segment_type)
            token_count = len(paragraph.split())
            
            segment = ContentSegment(
                content=paragraph.strip(),
                segment_type=segment_type,
                priority=priority,
                token_count=token_count
            )
            
            segments.append(segment)
        
        return segments
    
    def _determine_segment_type(self, paragraph: str, index: int, total: int) -> str:
        """确定段落类型"""
        if index == 0:
            return "introduction"
        elif index == total - 1:
            return "conclusion"
        elif any(keyword in paragraph.lower() for keyword in ["detail", "specific", "example"]):
            return "details"
        else:
            return "main"
    
    def _calculate_segment_priority(self, paragraph: str, segment_type: str) -> int:
        """计算段落优先级"""
        base_priority = {
            "introduction": 5,
            "main": 4,
            "details": 2,
            "conclusion": 3
        }.get(segment_type, 3)
        
        # 根据内容调整优先级
        high_priority_keywords = ["important", "must", "essential", "key", "critical"]
        if any(keyword in paragraph.lower() for keyword in high_priority_keywords):
            base_priority = min(5, base_priority + 1)
        
        return base_priority
```

## 3. 分页和延续机制

### 3.1 智能分页系统
**优化建议:**
```python
# 新建 app/core/pagination_manager.py
from typing import Dict, Any, Optional, List
import uuid
from datetime import datetime, timedelta

class PaginationManager:
    """分页管理器"""
    
    def __init__(self):
        self.continuation_store = {}
        self.cleanup_interval = timedelta(hours=2)
    
    def create_continuation(
        self,
        full_content: str,
        current_page_content: str,
        page_size: int,
        context: Dict[str, Any] = None
    ) -> Optional[str]:
        """创建延续token"""
        
        if len(full_content) <= len(current_page_content):
            return None  # 没有更多内容
        
        continuation_id = str(uuid.uuid4())
        
        # 计算剩余内容
        remaining_content = full_content[len(current_page_content):].strip()
        
        self.continuation_store[continuation_id] = {
            "remaining_content": remaining_content,
            "page_size": page_size,
            "context": context or {},
            "created_at": datetime.now(),
            "access_count": 0
        }
        
        return continuation_id
    
    def get_next_page(self, continuation_id: str) -> Optional[Dict[str, Any]]:
        """获取下一页内容"""
        
        if continuation_id not in self.continuation_store:
            return None
        
        continuation_data = self.continuation_store[continuation_id]
        continuation_data["access_count"] += 1
        
        remaining_content = continuation_data["remaining_content"]
        page_size = continuation_data["page_size"]
        
        if not remaining_content:
            # 清理已完成的延续
            del self.continuation_store[continuation_id]
            return None
        
        # 智能分页：尝试在句子边界分页
        next_page_content = self._extract_next_page(remaining_content, page_size)
        
        # 更新剩余内容
        continuation_data["remaining_content"] = remaining_content[len(next_page_content):].strip()
        
        # 检查是否还有更多内容
        has_more = len(continuation_data["remaining_content"]) > 0
        next_continuation_id = continuation_id if has_more else None
        
        if not has_more:
            del self.continuation_store[continuation_id]
        
        return {
            "content": next_page_content,
            "has_more": has_more,
            "continuation_id": next_continuation_id,
            "page_info": {
                "estimated_remaining_pages": self._estimate_remaining_pages(
                    continuation_data["remaining_content"], page_size
                ) if has_more else 0
            }
        }
    
    def _extract_next_page(self, content: str, target_size: int) -> str:
        """智能提取下一页内容"""
        
        if len(content) <= target_size:
            return content
        
        # 尝试在句子边界分页
        sentences = content.split('. ')
        
        current_length = 0
        selected_sentences = []
        
        for sentence in sentences:
            sentence_with_period = sentence + '. ' if not sentence.endswith('.') else sentence + ' '
            
            if current_length + len(sentence_with_period) <= target_size:
                selected_sentences.append(sentence_with_period)
                current_length += len(sentence_with_period)
            else:
                break
        
        if selected_sentences:
            return ''.join(selected_sentences).strip()
        
        # 如果无法在句子边界分页，直接截断
        return content[:target_size].strip()
    
    def _estimate_remaining_pages(self, remaining_content: str, page_size: int) -> int:
        """估算剩余页数"""
        if not remaining_content:
            return 0
        
        return max(1, len(remaining_content) // page_size)
    
    async def cleanup_expired_continuations(self):
        """清理过期的延续数据"""
        now = datetime.now()
        expired_keys = []
        
        for continuation_id, data in self.continuation_store.items():
            if now - data["created_at"] > self.cleanup_interval:
                expired_keys.append(continuation_id)
        
        for key in expired_keys:
            del self.continuation_store[key]
        
        return len(expired_keys)
```

## 4. 实施优先级和时间表

### 高优先级 (立即实施)
1. **动态长度控制器** - 1周
2. **内容分段和摘要** - 2周
3. **基础分页机制** - 1周

### 中优先级 (2-3周内)
1. **智能摘要器** - 2周
2. **用户偏好学习** - 2周
3. **性能优化** - 1周

### 低优先级 (长期优化)
1. **高级摘要算法** - 3周
2. **个性化推荐** - 4周
3. **A/B测试框架** - 2周

## 5. 监控和指标

### 关键指标
- 响应长度分布
- 用户满意度评分
- 延续使用率
- 摘要质量评分
- 响应时间改善

### 6.1 用户体验指标
```python
# 新建 app/core/ux_metrics.py
class UXMetricsCollector:
    def __init__(self):
        self.metrics = {
            "response_satisfaction": [],
            "length_preferences": {},
            "continuation_usage": [],
            "reading_completion_rate": []
        }

    async def track_user_interaction(
        self,
        user_id: str,
        response_length: int,
        user_rating: Optional[int],
        read_completion: float,
        used_continuation: bool
    ):
        """跟踪用户交互指标"""
        interaction_data = {
            "user_id": user_id,
            "response_length": response_length,
            "user_rating": user_rating,
            "read_completion": read_completion,
            "used_continuation": used_continuation,
            "timestamp": datetime.now()
        }

        # 存储指标用于分析
        await self._store_interaction_data(interaction_data)
```

## 总结

这些AI输出长度控制建议将显著提升SynTour系统的用户体验和响应效率。建议按照优先级逐步实施，并持续收集用户反馈以优化控制策略。
