globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/providers/LanguageProvider.tsx":{"*":{"id":"(ssr)/./src/providers/LanguageProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/ReduxProvider.tsx":{"*":{"id":"(ssr)/./src/providers/ReduxProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/ThemeProvider.tsx":{"*":{"id":"(ssr)/./src/providers/ThemeProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/ui/Header.tsx":{"*":{"id":"(ssr)/./src/ui/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/ui/ToastContainer.tsx":{"*":{"id":"(ssr)/./src/ui/ToastContainer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/home/<USER>":{"*":{"id":"(ssr)/./app/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/auth/login/page.tsx":{"*":{"id":"(ssr)/./app/auth/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\syntour\\syntour\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Plus_Jakarta_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"600\",\"700\",\"800\"],\"variable\":\"--font-display\",\"display\":\"swap\"}],\"variableName\":\"display\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Plus_Jakarta_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"600\",\"700\",\"800\"],\"variable\":\"--font-display\",\"display\":\"swap\"}],\"variableName\":\"display\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\"],\"variable\":\"--font-body\",\"display\":\"swap\"}],\"variableName\":\"body\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\"],\"variable\":\"--font-body\",\"display\":\"swap\"}],\"variableName\":\"body\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\src\\providers\\LanguageProvider.tsx":{"id":"(app-pages-browser)/./src/providers/LanguageProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\src\\providers\\ReduxProvider.tsx":{"id":"(app-pages-browser)/./src/providers/ReduxProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\src\\providers\\ThemeProvider.tsx":{"id":"(app-pages-browser)/./src/providers/ThemeProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\src\\ui\\Header.tsx":{"id":"(app-pages-browser)/./src/ui/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\src\\ui\\ToastContainer.tsx":{"id":"(app-pages-browser)/./src/ui/ToastContainer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\syntour\\syntour\\frontend\\app\\home\\page.tsx":{"id":"(app-pages-browser)/./app/home/<USER>","name":"*","chunks":[],"async":false},"E:\\syntour\\syntour\\frontend\\app\\auth\\login\\page.tsx":{"id":"(app-pages-browser)/./app/auth/login/page.tsx","name":"*","chunks":["app/auth/login/page","static/chunks/app/auth/login/page.js"],"async":false}},"entryCSSFiles":{"E:\\syntour\\syntour\\frontend\\":[],"E:\\syntour\\syntour\\frontend\\app\\page":[],"E:\\syntour\\syntour\\frontend\\app\\layout":["static/css/app/layout.css"],"E:\\syntour\\syntour\\frontend\\app\\auth\\login\\page":[]}}