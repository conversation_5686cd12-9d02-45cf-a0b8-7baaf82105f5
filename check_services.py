#!/usr/bin/env python3
"""
服务状态检查脚本
Service Status Checker

检查前后端服务是否正常运行
"""

import requests
import socket
import json
import time
from datetime import datetime

class Colors:
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    OKBLUE = '\033[94m'

def print_colored(message, color=Colors.OKBLUE):
    print(f"{color}{message}{Colors.ENDC}")

def print_header(message):
    print_colored(f"\n{'='*60}", Colors.BOLD)
    print_colored(f"  {message}", Colors.BOLD)
    print_colored(f"{'='*60}", Colors.BOLD)

def check_port(host, port, service_name):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print_colored(f"✓ {service_name} 端口 {port} 开放", Colors.OKGREEN)
            return True
        else:
            print_colored(f"✗ {service_name} 端口 {port} 未开放", Colors.FAIL)
            return False
    except Exception as e:
        print_colored(f"✗ {service_name} 端口检查失败: {e}", Colors.FAIL)
        return False

def check_backend_api():
    """检查后端API"""
    print_header("检查后端API服务")
    
    base_url = "http://localhost:8000"
    
    # 检查基本健康状态
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print_colored("✓ 后端基本健康检查通过", Colors.OKGREEN)
        else:
            print_colored(f"✗ 后端健康检查失败: {response.status_code}", Colors.FAIL)
            return False
    except Exception as e:
        print_colored(f"✗ 后端健康检查异常: {e}", Colors.FAIL)
        return False
    
    # 检查语音识别健康状态
    try:
        response = requests.get(f"{base_url}/api/speech/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                health_data = data.get('data', {})
                status = health_data.get('status', 'unknown')
                print_colored(f"✓ 语音识别健康状态: {status}", Colors.OKGREEN)
                
                metrics = health_data.get('metrics', {})
                print_colored(f"  成功率: {metrics.get('success_rate', 0):.2%}", Colors.OKBLUE)
                print_colored(f"  平均处理时间: {metrics.get('avg_processing_time', 0):.2f}秒", Colors.OKBLUE)
                print_colored(f"  活跃会话: {metrics.get('active_sessions', 0)}", Colors.OKBLUE)
            else:
                print_colored("✗ 语音识别健康检查返回错误", Colors.FAIL)
        else:
            print_colored(f"✗ 语音识别健康检查失败: {response.status_code}", Colors.FAIL)
    except Exception as e:
        print_colored(f"✗ 语音识别健康检查异常: {e}", Colors.FAIL)
    
    # 检查语音识别统计
    try:
        response = requests.get(f"{base_url}/api/speech/stats", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                stats = data.get('data', {}).get('global_stats', {})
                print_colored("✓ 语音识别统计获取成功", Colors.OKGREEN)
                print_colored(f"  总请求: {stats.get('total_requests', 0)}", Colors.OKBLUE)
                print_colored(f"  成功请求: {stats.get('successful_requests', 0)}", Colors.OKBLUE)
                print_colored(f"  失败请求: {stats.get('failed_requests', 0)}", Colors.OKBLUE)
            else:
                print_colored("✗ 语音识别统计返回错误", Colors.FAIL)
        else:
            print_colored(f"✗ 语音识别统计获取失败: {response.status_code}", Colors.FAIL)
    except Exception as e:
        print_colored(f"✗ 语音识别统计检查异常: {e}", Colors.FAIL)
    
    # 检查API文档
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print_colored("✓ API文档可访问", Colors.OKGREEN)
        else:
            print_colored(f"✗ API文档访问失败: {response.status_code}", Colors.FAIL)
    except Exception as e:
        print_colored(f"✗ API文档检查异常: {e}", Colors.FAIL)
    
    return True

def check_frontend():
    """检查前端服务"""
    print_header("检查前端服务")
    
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200:
            print_colored("✓ 前端服务正常运行", Colors.OKGREEN)
            print_colored(f"  响应时间: {response.elapsed.total_seconds():.2f}秒", Colors.OKBLUE)
            return True
        else:
            print_colored(f"✗ 前端服务异常: {response.status_code}", Colors.FAIL)
            return False
    except Exception as e:
        print_colored(f"✗ 前端服务检查失败: {e}", Colors.FAIL)
        return False

def show_service_urls():
    """显示服务URL"""
    print_header("服务访问地址")
    
    print_colored("🌐 前端服务:", Colors.OKBLUE)
    print_colored("   主页: http://localhost:3000", Colors.OKBLUE)
    print_colored("   首页: http://localhost:3000/home", Colors.OKBLUE)
    
    print_colored("\n🔧 后端服务:", Colors.OKBLUE)
    print_colored("   API根地址: http://localhost:8000", Colors.OKBLUE)
    print_colored("   API文档: http://localhost:8000/docs", Colors.OKBLUE)
    print_colored("   健康检查: http://localhost:8000/health", Colors.OKBLUE)
    
    print_colored("\n🎤 语音识别监控:", Colors.OKBLUE)
    print_colored("   健康状态: http://localhost:8000/api/speech/health", Colors.OKBLUE)
    print_colored("   统计信息: http://localhost:8000/api/speech/stats", Colors.OKBLUE)
    print_colored("   活跃会话: http://localhost:8000/api/speech/sessions", Colors.OKBLUE)

def show_test_instructions():
    """显示测试说明"""
    print_header("语音识别测试说明")
    
    print_colored("🎤 WebSocket测试 (在浏览器控制台运行):", Colors.OKBLUE)
    print_colored("""
const ws = new WebSocket('ws://localhost:8000/ws/speech/test_client');

ws.onopen = function() {
    console.log('语音识别连接已建立');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
    
    if (data.type === 'connection') {
        console.log('连接模式:', data.mode);
    } else if (data.type === 'transcript') {
        console.log('识别结果:', data.transcript);
        console.log('音频质量:', data.audio_quality);
        console.log('置信度:', data.confidence);
    }
};

// 发送测试音频数据
const testAudio = new ArrayBuffer(1000);
ws.send(testAudio);
""", Colors.OKBLUE)
    
    print_colored("\n📊 API测试 (命令行):", Colors.OKBLUE)
    print_colored("curl http://localhost:8000/api/speech/health", Colors.OKBLUE)
    print_colored("curl http://localhost:8000/api/speech/stats", Colors.OKBLUE)
    print_colored("curl http://localhost:8000/api/speech/sessions", Colors.OKBLUE)

def main():
    """主函数"""
    print_header(f"SynTour 服务状态检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_good = True
    
    # 检查端口
    print_header("检查服务端口")
    backend_port_ok = check_port('localhost', 8000, '后端服务')
    frontend_port_ok = check_port('localhost', 3000, '前端服务')
    
    if not backend_port_ok:
        print_colored("\n⚠️  后端服务未启动，请运行:", Colors.WARNING)
        print_colored("cd syntour/backend && python -m uvicorn main:app --reload", Colors.WARNING)
        all_good = False
    
    if not frontend_port_ok:
        print_colored("\n⚠️  前端服务未启动，请运行:", Colors.WARNING)
        print_colored("cd syntour/frontend && npm run dev", Colors.WARNING)
        all_good = False
    
    # 检查后端API
    if backend_port_ok:
        backend_api_ok = check_backend_api()
        if not backend_api_ok:
            all_good = False
    
    # 检查前端
    if frontend_port_ok:
        frontend_ok = check_frontend()
        if not frontend_ok:
            all_good = False
    
    # 显示结果
    print_header("检查结果")
    if all_good:
        print_colored("🎉 所有服务运行正常！", Colors.OKGREEN)
        show_service_urls()
        show_test_instructions()
    else:
        print_colored("❌ 部分服务存在问题，请查看上述信息", Colors.FAIL)
        print_colored("\n🔧 快速启动命令:", Colors.WARNING)
        print_colored("双击运行: start_dev.bat", Colors.WARNING)
        print_colored("或运行: python start_dev_servers.py", Colors.WARNING)

if __name__ == "__main__":
    main()
