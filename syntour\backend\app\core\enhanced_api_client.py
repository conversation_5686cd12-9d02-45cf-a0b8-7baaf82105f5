# app/core/enhanced_api_client.py
import asyncio
import aiohttp
import logging
import time
import json
import hashlib
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field
from urllib.parse import urlencode
import random

from .connection_pool import HTTPConnectionP<PERSON>, PoolConfig, PoolStats
from .circuit_breaker import Circuit<PERSON>reaker, CircuitBreakerConfig, circuit_breaker_manager
from .api_config import APIConfig

logger = logging.getLogger(__name__)

@dataclass
class RequestBatch:
    """Batch of API requests for optimization"""
    requests: List[Dict[str, Any]] = field(default_factory=list)
    max_size: int = 10
    timeout: float = 5.0
    created_at: float = field(default_factory=time.time)

@dataclass
class CacheEntry:
    """Cache entry with TTL"""
    data: Any
    created_at: float
    ttl: float
    
    def is_expired(self) -> bool:
        return time.time() - self.created_at > self.ttl

@dataclass
class APIMetrics:
    """API performance metrics"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    cached_requests: int = 0
    avg_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    last_request_time: Optional[float] = None

class EnhancedAPIClient:
    """Enhanced API client with connection pooling, circuit breakers, and intelligent caching"""
    
    def __init__(self, api_name: str, base_url: str, config: Optional[Dict] = None):
        self.api_name = api_name
        self.base_url = base_url.rstrip('/')
        self.config = config or {}
        
        # Initialize connection pool
        pool_config = PoolConfig(
            min_size=self.config.get('pool_min_size', 5),
            max_size=self.config.get('pool_max_size', 20),
            timeout=self.config.get('timeout', 30.0),
            health_check_interval=self.config.get('health_check_interval', 30.0)
        )
        self.connection_pool = HTTPConnectionPool(pool_config)
        
        # Initialize circuit breaker
        breaker_config = CircuitBreakerConfig(
            failure_threshold=self.config.get('failure_threshold', 5),
            recovery_timeout=self.config.get('recovery_timeout', 60.0),
            success_threshold=self.config.get('success_threshold', 3),
            timeout=self.config.get('request_timeout', 30.0),
            name=f"{api_name}_circuit_breaker"
        )
        self.circuit_breaker = circuit_breaker_manager.create_breaker(
            f"{api_name}_breaker", breaker_config
        )
        
        # Initialize caching
        self.cache: Dict[str, CacheEntry] = {}
        self.cache_enabled = self.config.get('cache_enabled', True)
        self.default_cache_ttl = self.config.get('cache_ttl', 300)  # 5 minutes
        
        # Initialize request batching
        self.batching_enabled = self.config.get('batching_enabled', False)
        self.current_batch = RequestBatch()
        self.batch_processors: Dict[str, Callable] = {}
        
        # Initialize metrics
        self.metrics = APIMetrics()
        
        # Rate limiting
        self.rate_limit = self.config.get('rate_limit', 100)  # requests per minute
        self.rate_limit_window = 60.0  # 1 minute
        self.request_times: List[float] = []
        
        # Retry configuration
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_backoff_factor = self.config.get('retry_backoff_factor', 2)
        self.retry_jitter = self.config.get('retry_jitter', True)
        
    async def initialize(self):
        """Initialize the enhanced API client"""
        await self.connection_pool.initialize()
        
        # Add fallback handlers to circuit breaker
        self.circuit_breaker.add_fallback_handler(self._cache_fallback)
        self.circuit_breaker.add_fallback_handler(self._default_fallback)
        
        logger.info(f"Enhanced API client for {self.api_name} initialized")
    
    def _generate_cache_key(self, method: str, url: str, params: Optional[Dict] = None, 
                          data: Optional[Dict] = None) -> str:
        """Generate cache key for request"""
        key_data = f"{method}:{url}"
        if params:
            key_data += f":{urlencode(sorted(params.items()))}"
        if data:
            key_data += f":{json.dumps(data, sort_keys=True)}"
        
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """Get data from cache if available and not expired"""
        if not self.cache_enabled or cache_key not in self.cache:
            return None
        
        entry = self.cache[cache_key]
        if entry.is_expired():
            del self.cache[cache_key]
            return None
        
        self.metrics.cached_requests += 1
        logger.debug(f"Cache hit for {self.api_name}: {cache_key}")
        return entry.data
    
    def _set_cache(self, cache_key: str, data: Any, ttl: Optional[float] = None):
        """Set data in cache"""
        if not self.cache_enabled:
            return
        
        ttl = ttl or self.default_cache_ttl
        self.cache[cache_key] = CacheEntry(
            data=data,
            created_at=time.time(),
            ttl=ttl
        )
        
        # Clean up expired entries periodically
        if len(self.cache) % 100 == 0:
            self._cleanup_cache()
    
    def _cleanup_cache(self):
        """Remove expired cache entries"""
        expired_keys = [
            key for key, entry in self.cache.items() 
            if entry.is_expired()
        ]
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        current_time = time.time()
        
        # Remove old request times outside the window
        self.request_times = [
            t for t in self.request_times 
            if current_time - t < self.rate_limit_window
        ]
        
        # Check if we're over the rate limit
        if len(self.request_times) >= self.rate_limit:
            sleep_time = self.rate_limit_window - (current_time - self.request_times[0])
            if sleep_time > 0:
                logger.warning(f"Rate limit reached for {self.api_name}, sleeping for {sleep_time:.2f}s")
                await asyncio.sleep(sleep_time)
        
        self.request_times.append(current_time)
    
    def _add_jitter(self, delay: float) -> float:
        """Add jitter to delay to prevent thundering herd"""
        if not self.retry_jitter:
            return delay
        
        jitter = random.uniform(0.1, 0.5) * delay
        return delay + jitter
    
    async def _cache_fallback(self, *args, **kwargs):
        """Fallback to cached data when circuit is open"""
        # Try to find any cached data for this API
        for cache_key, entry in self.cache.items():
            if not entry.is_expired():
                logger.info(f"Using cached fallback data for {self.api_name}")
                return {
                    "success": True,
                    "data": entry.data,
                    "source": "cache_fallback",
                    "cached": True
                }
        return None
    
    async def _default_fallback(self, *args, **kwargs):
        """Default fallback response"""
        return {
            "success": False,
            "error": f"Service {self.api_name} is temporarily unavailable",
            "fallback": True,
            "retry_after": self.circuit_breaker.config.recovery_timeout
        }
    
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with all enhancements"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        # Check cache first for GET requests
        cache_key = None
        if method.upper() == 'GET' and self.cache_enabled:
            cache_key = self._generate_cache_key(method, url, kwargs.get('params'))
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return {
                    "success": True,
                    "data": cached_data,
                    "cached": True,
                    "source": "cache"
                }
        
        # Check rate limit
        await self._check_rate_limit()
        
        # Make request through circuit breaker
        start_time = time.time()
        
        async def _request_func():
            return await self.connection_pool.request(method, url, **kwargs)
        
        try:
            response = await self.circuit_breaker.call(_request_func)
            
            # Process response
            response_time = time.time() - start_time
            self._update_metrics(response_time, True)
            
            # Parse response
            response_text = await response.text()
            
            if 200 <= response.status < 300:
                try:
                    data = json.loads(response_text) if response_text else {}
                except json.JSONDecodeError:
                    data = {"content": response_text}
                
                # Cache successful GET responses
                if method.upper() == 'GET' and cache_key and self.cache_enabled:
                    self._set_cache(cache_key, data)
                
                return {
                    "success": True,
                    "data": data,
                    "status_code": response.status,
                    "response_time": response_time,
                    "cached": False
                }
            else:
                error_msg = self._parse_error_message(response_text, response.status)
                return {
                    "success": False,
                    "error": error_msg,
                    "status_code": response.status,
                    "response_time": response_time
                }
                
        except Exception as e:
            response_time = time.time() - start_time
            self._update_metrics(response_time, False)
            
            logger.error(f"Request to {self.api_name} failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "response_time": response_time
            }
    
    def _update_metrics(self, response_time: float, success: bool):
        """Update API metrics"""
        self.metrics.total_requests += 1
        self.metrics.last_request_time = time.time()
        
        if success:
            self.metrics.successful_requests += 1
        else:
            self.metrics.failed_requests += 1
        
        # Update response time metrics
        self.metrics.avg_response_time = (
            (self.metrics.avg_response_time * (self.metrics.total_requests - 1) + response_time) 
            / self.metrics.total_requests
        )
        self.metrics.min_response_time = min(self.metrics.min_response_time, response_time)
        self.metrics.max_response_time = max(self.metrics.max_response_time, response_time)
    
    def _parse_error_message(self, response_text: str, status_code: int) -> str:
        """Parse error message from API response"""
        try:
            error_data = json.loads(response_text)
            error_fields = ['message', 'error', 'detail', 'error_description', 'msg']
            
            for field in error_fields:
                if field in error_data:
                    return str(error_data[field])
            
            return str(error_data)[:200] + "..." if len(str(error_data)) > 200 else str(error_data)
            
        except json.JSONDecodeError:
            return response_text[:200] + "..." if len(response_text) > 200 else response_text
    
    # HTTP method wrappers
    async def get(self, endpoint: str, params: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """Make GET request"""
        return await self._make_request('GET', endpoint, params=params, **kwargs)
    
    async def post(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """Make POST request"""
        return await self._make_request('POST', endpoint, data=data, json=json_data, **kwargs)
    
    async def put(self, endpoint: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """Make PUT request"""
        return await self._make_request('PUT', endpoint, data=data, json=json_data, **kwargs)
    
    async def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make DELETE request"""
        return await self._make_request('DELETE', endpoint, **kwargs)
    
    def get_metrics(self) -> APIMetrics:
        """Get current API metrics"""
        return self.metrics
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        pool_stats = self.connection_pool.get_stats()
        breaker_stats = self.circuit_breaker.get_stats()
        
        return {
            "api_name": self.api_name,
            "connection_pool": {
                "health": pool_stats.health_status,
                "total_connections": pool_stats.total_connections,
                "active_connections": pool_stats.active_connections,
                "total_requests": pool_stats.total_requests,
                "failed_requests": pool_stats.failed_requests,
                "avg_response_time": pool_stats.avg_response_time
            },
            "circuit_breaker": {
                "state": breaker_stats.state.value,
                "failure_count": breaker_stats.failure_count,
                "success_count": breaker_stats.success_count,
                "total_requests": breaker_stats.total_requests,
                "open_count": breaker_stats.open_count
            },
            "cache": {
                "enabled": self.cache_enabled,
                "entries": len(self.cache),
                "hit_rate": (
                    self.metrics.cached_requests / self.metrics.total_requests 
                    if self.metrics.total_requests > 0 else 0
                )
            },
            "metrics": {
                "total_requests": self.metrics.total_requests,
                "success_rate": (
                    self.metrics.successful_requests / self.metrics.total_requests 
                    if self.metrics.total_requests > 0 else 0
                ),
                "avg_response_time": self.metrics.avg_response_time,
                "min_response_time": self.metrics.min_response_time if self.metrics.min_response_time != float('inf') else 0,
                "max_response_time": self.metrics.max_response_time
            }
        }
    
    async def close(self):
        """Close the API client and cleanup resources"""
        await self.connection_pool.close()
        self.cache.clear()
        logger.info(f"Enhanced API client for {self.api_name} closed")
